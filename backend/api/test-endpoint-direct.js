// Direct test of the playback token generation without authentication
const axios = require('axios');

async function testPlaybackTokenEndpoint() {
  console.log('🎬 Testing Playback Token Endpoint');
  console.log('==================================\n');

  const baseUrl = 'http://localhost:8000/api';
  const eventId = 'event-1748546908092';

  try {
    // First, test if the event exists
    console.log('📋 Testing event existence...');
    const eventResponse = await axios.get(`${baseUrl}/events/${eventId}`);
    console.log(`✅ Event found: ${eventResponse.data.title}`);
    console.log(`   Channel: ${eventResponse.data.channel?.name || 'Unknown'}`);
    console.log(`   Price: ${eventResponse.data.price} ${eventResponse.data.currency}\n`);

    // Test the playback token endpoint (this will fail due to auth, but we'll see the error)
    console.log('🔑 Testing playback token endpoint...');
    try {
      const tokenResponse = await axios.post(`${baseUrl}/events/${eventId}/playback-token`, {
        durationMinutes: 10
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token' // This will fail, but we'll see the specific error
        }
      });
      
      console.log('✅ Token generated successfully!');
      console.log('📊 Response:', JSON.stringify(tokenResponse.data, null, 2));
      
    } catch (authError) {
      if (authError.response) {
        console.log(`❌ Authentication error (expected): ${authError.response.status}`);
        console.log(`   Error: ${authError.response.data?.message || 'Unknown error'}`);
        
        if (authError.response.status === 401) {
          console.log('\n🔍 This confirms the endpoint exists and authentication is working.');
          console.log('   The issue is with the token you\'re using.');
          console.log('\n💡 Solutions:');
          console.log('   1. Get a fresh token from Keycloak');
          console.log('   2. Check if your token has expired');
          console.log('   3. Verify the token format is correct');
        }
      } else {
        console.log(`❌ Network error: ${authError.message}`);
      }
    }

    // Test a public endpoint to verify backend is working
    console.log('\n🌐 Testing public endpoint...');
    const publicResponse = await axios.get(`${baseUrl}/events`);
    console.log(`✅ Public endpoint working: Found ${publicResponse.data.events?.length || 0} events`);

  } catch (error) {
    console.error('\n❌ Test failed:');
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Error: ${error.response.data?.message || 'Unknown error'}`);
    } else {
      console.error(`   Network error: ${error.message}`);
    }
  }
}

// Test the endpoint
testPlaybackTokenEndpoint();

const { Neo4jService } = require('./dist/modules/database/neo4j.service');

// Load environment variables
require('dotenv').config();

async function debugEventChannelRelationships() {
  console.log('🔍 Debugging Event-Channel Relationships');
  console.log('=========================================\n');

  const neo4jService = new Neo4jService({
    uri: process.env.NEO4J_URI,
    username: process.env.NEO4J_USERNAME,
    password: process.env.NEO4J_PASSWORD,
  });

  try {
    // Check all events and their channel relationships
    console.log('📋 Checking Event-Channel Relationships:');
    const result = await neo4jService.read(`
      MATCH (e:Event)
      OPTIONAL MATCH (ch:Channel)-[:HOSTS]->(e)
      RETURN e.id AS eventId, e.title AS eventTitle, 
             ch.id AS channelId, ch.name AS channelName,
             ch.channelArn AS channelArn
      ORDER BY e.createdAt DESC
      LIMIT 10
    `);

    if (result.records.length === 0) {
      console.log('❌ No events found in database');
      return;
    }

    result.records.forEach((record, index) => {
      const eventId = record.get('eventId');
      const eventTitle = record.get('eventTitle');
      const channelId = record.get('channelId');
      const channelName = record.get('channelName');
      const channelArn = record.get('channelArn');

      console.log(`${index + 1}. Event: ${eventId}`);
      console.log(`   Title: ${eventTitle}`);
      
      if (channelId) {
        console.log(`   ✅ Channel: ${channelId} (${channelName})`);
        console.log(`   ✅ Channel ARN: ${channelArn || 'Not set'}`);
      } else {
        console.log(`   ❌ No channel relationship found!`);
      }
      console.log('');
    });

    // Check for orphaned events (events without channels)
    console.log('🔍 Checking for Orphaned Events:');
    const orphanedResult = await neo4jService.read(`
      MATCH (e:Event)
      WHERE NOT EXISTS((ch:Channel)-[:HOSTS]->(e))
      RETURN e.id AS eventId, e.title AS eventTitle
      LIMIT 5
    `);

    if (orphanedResult.records.length > 0) {
      console.log('❌ Found orphaned events (no channel relationship):');
      orphanedResult.records.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.get('eventId')} - ${record.get('eventTitle')}`);
      });
    } else {
      console.log('✅ No orphaned events found');
    }

    // Check for channels without ARNs
    console.log('\n🔍 Checking Channels without ARNs:');
    const noArnResult = await neo4jService.read(`
      MATCH (ch:Channel)
      WHERE ch.channelArn IS NULL OR ch.channelArn = ''
      RETURN ch.id AS channelId, ch.name AS channelName
      LIMIT 5
    `);

    if (noArnResult.records.length > 0) {
      console.log('❌ Found channels without ARNs:');
      noArnResult.records.forEach((record, index) => {
        console.log(`   ${index + 1}. ${record.get('channelId')} - ${record.get('channelName')}`);
      });
    } else {
      console.log('✅ All channels have ARNs');
    }

    console.log('\n🎯 Recommendations:');
    console.log('1. Use event IDs that have proper channel relationships');
    console.log('2. If you see orphaned events, they need to be linked to channels');
    console.log('3. If channels don\'t have ARNs, they need AWS IVS setup');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await neo4jService.close();
  }
}

debugEventChannelRelationships();

const { IvsClient, GetPlaybackRestrictionPolicyCommand, ListPlaybackKeyPairsCommand, GetPlaybackKeyPairCommand } = require('@aws-sdk/client-ivs');

// Load environment variables
require('dotenv').config();

async function checkKeyPairAssociation() {
  console.log('🔍 Checking Key Pair Association');
  console.log('================================\n');

  const region = 'eu-west-1';
  const keyPairId = '1itsv8wzkivZ';
  const policyArn = 'arn:aws:ivs:eu-west-1:223358806502:playback-restriction-policy/nLTAEjky6dlx';

  // Initialize AWS IVS client
  const ivsClient = new IvsClient({ 
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    }
  });

  try {
    // Step 1: Check if the key pair exists
    console.log('🔑 Step 1: Checking key pair existence...');
    try {
      const getKeyPairCommand = new GetPlaybackKeyPairCommand({ arn: `arn:aws:ivs:eu-west-1:223358806502:playback-key-pair/${keyPairId}` });
      const keyPairResponse = await ivsClient.send(getKeyPairCommand);
      
      console.log('✅ Key pair found:');
      console.log(`   Name: ${keyPairResponse.keyPair.name}`);
      console.log(`   ARN: ${keyPairResponse.keyPair.arn}`);
      console.log(`   Fingerprint: ${keyPairResponse.keyPair.fingerprint}\n`);
    } catch (error) {
      console.log(`❌ Key pair not found: ${error.message}\n`);
      
      // List all key pairs to see what's available
      console.log('📋 Listing all available key pairs...');
      const listKeyPairsCommand = new ListPlaybackKeyPairsCommand({});
      const listResponse = await ivsClient.send(listKeyPairsCommand);
      
      if (listResponse.keyPairs && listResponse.keyPairs.length > 0) {
        console.log('Available key pairs:');
        listResponse.keyPairs.forEach((kp, index) => {
          console.log(`   ${index + 1}. Name: ${kp.name}, ARN: ${kp.arn}`);
        });
      } else {
        console.log('No key pairs found in this account/region');
      }
      console.log('');
    }

    // Step 2: Check the playback restriction policy
    console.log('🔒 Step 2: Checking playback restriction policy...');
    const getPolicyCommand = new GetPlaybackRestrictionPolicyCommand({ arn: policyArn });
    const policyResponse = await ivsClient.send(getPolicyCommand);
    
    console.log('✅ Policy found:');
    console.log(`   Name: ${policyResponse.playbackRestrictionPolicy.name}`);
    console.log(`   ARN: ${policyResponse.playbackRestrictionPolicy.arn}`);
    console.log(`   Allowed Countries: ${policyResponse.playbackRestrictionPolicy.allowedCountries?.join(', ') || 'All'}`);
    console.log(`   Allowed Origins: ${policyResponse.playbackRestrictionPolicy.allowedOrigins?.join(', ') || 'All'}`);
    console.log(`   Strict Origin Enforcement: ${policyResponse.playbackRestrictionPolicy.enableStrictOriginEnforcement}\n`);

    // Step 3: Important note about key pair association
    console.log('🔍 Step 3: Key Pair Association Analysis');
    console.log('=======================================');
    console.log('ℹ️  IMPORTANT: In AWS IVS, playback key pairs are NOT directly associated');
    console.log('   with playback restriction policies in the policy configuration.');
    console.log('');
    console.log('   Instead, the association happens when:');
    console.log('   1. You generate a JWT token using a specific key pair');
    console.log('   2. The token is used to access a channel with a playback restriction policy');
    console.log('   3. AWS IVS validates the token signature against available key pairs');
    console.log('');
    console.log('🎯 Current Status:');
    console.log('   ✅ Channel has playback restriction policy');
    console.log('   ✅ Key pair exists (if found above)');
    console.log('   ✅ JWT tokens are signed with the correct key pair');
    console.log('');
    console.log('🔍 Possible Issues:');
    console.log('   1. Key pair might not be in the same region as the channel');
    console.log('   2. Key pair might not have the correct permissions');
    console.log('   3. There might be a delay in AWS IVS policy propagation');
    console.log('   4. The channel might need time to recognize the new policy');
    console.log('');
    console.log('💡 Recommendations:');
    console.log('   1. Wait 5-10 minutes for AWS IVS policy propagation');
    console.log('   2. Generate a fresh JWT token');
    console.log('   3. Test the playback URL again');
    console.log('   4. Check AWS CloudWatch logs for any errors');

  } catch (error) {
    console.error('\n❌ Check failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.name === 'AccessDeniedException') {
      console.error('\n💡 Solution: Check AWS credentials and permissions');
    } else if (error.name === 'ResourceNotFoundException') {
      console.error('\n💡 Solution: Verify the ARNs are correct');
    }
  }
}

checkKeyPairAssociation();

const axios = require('axios');

async function testFreshToken() {
  console.log('🎬 Testing Fresh Playback Token');
  console.log('===============================\n');

  const freshToken = 'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJoTFNqbnB6M1YxbFNyUGh3UWlYVTYtUEVEYXM0WHdaTWNlWWp2ZkFWTXNBIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jDDlU697zsFapLb59TqnrATeQX8kWgE33WteA9GPfn14tw_5G_pRXceu4_JO2wlqhkhUcX0_G8l-Rhhd6p0QfsyEnK0MreGmAlG0Ka43Br1-tpzROfRCCmKgHiq1HhHPsNTfsA3WfCQpP4CUYeTxTv2kTNzhfH29ybR9JrQzYCebb6PVaQzcPQE6veTRNbBCoxoCpSuPuFs4K6NtFYAPgxVDCsKWyqn0JQwexiV4xdoADQnwWb-aeC7OKRy2jc-PG4mWMc4qWOLqLsX_3pvVGXlQ-X90hu3RYTTqm23cOFu5gEfbHd4F8KGGHqJDOnDEouXAvECuCaOUWs6IghX5Wg';

  try {
    // Step 1: Get fresh playback token from backend
    console.log('🔑 Step 1: Getting fresh playback token from backend...');
    const tokenResponse = await axios.post('http://localhost:8000/api/events/event-*************/playback-token', {
      durationMinutes: 10
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${freshToken}`
      },
      timeout: 15000
    });

    console.log('✅ Fresh playback token obtained!\n');
    
    const result = tokenResponse.data;
    console.log('📊 Token Details:');
    console.log(`   Requires Authorization: ${result.requiresAuthorization}`);
    console.log(`   Token Length: ${result.token?.length || 'N/A'} characters`);
    console.log(`   Expires In: ${result.expiresInMinutes} minutes`);
    console.log(`   Expires At: ${result.expiresAt}\n`);

    // Step 2: Test the fresh authorizedStreamUrl
    if (result.authorizedStreamUrl) {
      console.log('🎥 Step 2: Testing fresh authorizedStreamUrl...');
      console.log(`   URL: ${result.authorizedStreamUrl.substring(0, 100)}...\n`);
      
      try {
        const streamResponse = await axios.get(result.authorizedStreamUrl, { 
          timeout: 10000,
          validateStatus: function (status) {
            return status < 500; // Accept any status less than 500 as success for testing
          }
        });

        if (streamResponse.status === 200) {
          console.log('🎉 SUCCESS! Stream URL is working!');
          console.log(`   Status: ${streamResponse.status}`);
          console.log(`   Content-Type: ${streamResponse.headers['content-type']}`);
          console.log(`   Response: ${streamResponse.data.substring(0, 200)}...\n`);
          
          console.log('✅ COMPLETE SUCCESS!');
          console.log('   The channel is now properly configured for private access');
          console.log('   Your frontend should work correctly with this URL');
          
        } else if (streamResponse.status === 404) {
          console.log('❌ Still getting 404 error');
          console.log(`   Response: ${JSON.stringify(streamResponse.data)}`);
          
          if (streamResponse.data?.[0]?.error === 'Can not find channel') {
            console.log('\n🔍 Analysis: Channel still not found');
            console.log('   This might be due to:');
            console.log('   1. AWS IVS policy propagation delay (can take 5-15 minutes)');
            console.log('   2. Key pair not properly associated with the region');
            console.log('   3. Channel configuration issue');
            console.log('\n💡 Recommendations:');
            console.log('   1. Wait 10-15 minutes for AWS propagation');
            console.log('   2. Check AWS IVS console for any errors');
            console.log('   3. Verify key pair is in eu-west-1 region');
          }
        } else {
          console.log(`⚠️  Unexpected status: ${streamResponse.status}`);
          console.log(`   Response: ${JSON.stringify(streamResponse.data)}`);
        }

      } catch (streamError) {
        console.log('❌ Stream URL test failed:');
        if (streamError.response) {
          console.log(`   Status: ${streamError.response.status}`);
          console.log(`   Error: ${JSON.stringify(streamError.response.data)}`);
        } else {
          console.log(`   Network error: ${streamError.message}`);
        }
      }
    } else {
      console.log('❌ No authorizedStreamUrl in response');
    }

    // Step 3: Show the URLs for manual testing
    console.log('\n📋 URLs for Manual Testing:');
    console.log('============================');
    if (result.authorizedStreamUrl) {
      console.log('🎥 Authorized Stream URL (for iframe):');
      console.log(`   ${result.authorizedStreamUrl}`);
    }
    if (result.authorizedEmbedUrl) {
      console.log('\n🎮 Authorized Embed URL (for Player SDK):');
      console.log(`   ${result.authorizedEmbedUrl}`);
    }

  } catch (error) {
    console.error('\n❌ Test failed:');
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Error: ${error.response.data?.message || 'Unknown error'}`);
    } else {
      console.error(`   Network error: ${error.message}`);
    }
  }
}

testFreshToken();

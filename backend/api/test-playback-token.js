const { EventsService } = require('./dist/modules/events/events.service');
const { Neo4jService } = require('./dist/modules/database/neo4j.service');
const { PlaybackAuthService } = require('./dist/modules/streaming/playback-auth.service');
const { IvsService } = require('./dist/modules/streaming/ivs.service');

// Load environment variables
require('dotenv').config();

async function testPlaybackTokenGeneration() {
  console.log('🎬 Testing Playback Token Generation');
  console.log('====================================\n');

  // Initialize services
  const neo4jService = new Neo4jService({
    uri: process.env.NEO4J_URI,
    username: process.env.NEO4J_USERNAME,
    password: process.env.NEO4J_PASSWORD,
  });

  const playbackAuthService = new PlaybackAuthService();
  const ivsService = new IvsService();
  
  // Initialize IVS service with shared keys
  await ivsService.initializeSharedPlaybackKeys();
  
  const eventsService = new EventsService(
    neo4jService,
    null, // channelsService - not needed for this test
    null, // paymentsService - not needed for this test
    playbackAuthService,
    ivsService
  );

  try {
    const eventId = 'event-1748546908092';
    const userId = 'test-user-123';

    console.log(`📋 Testing event: ${eventId}`);
    console.log(`👤 Testing user: ${userId}\n`);

    // Test the playback token generation
    console.log('🔑 Generating playback token...');
    const result = await eventsService.generatePlaybackToken(eventId, userId, 10);

    console.log('✅ Playback token generated successfully!\n');
    
    console.log('📊 Token Details:');
    console.log(`   Requires Authorization: ${result.requiresAuthorization}`);
    console.log(`   Token Length: ${result.token ? result.token.length : 'N/A'} characters`);
    console.log(`   Expires In: ${result.expiresInMinutes} minutes`);
    console.log(`   Expires At: ${result.expiresAt}`);
    
    console.log('\n🔗 URLs Generated:');
    console.log(`   Authorized Stream URL: ${result.authorizedStreamUrl ? 'Generated' : 'Not available'}`);
    console.log(`   Authorized Embed URL: ${result.authorizedEmbedUrl ? 'Generated' : 'Not available'}`);
    
    if (result.authorizedStreamUrl) {
      console.log(`\n📺 Stream URL Preview:`);
      console.log(`   ${result.authorizedStreamUrl.substring(0, 100)}...`);
      
      // Check if it has the correct token parameter
      if (result.authorizedStreamUrl.includes('?token=')) {
        console.log('   ✅ Uses correct "token" parameter for HLS URL');
      } else if (result.authorizedStreamUrl.includes('?aws_auth_token=')) {
        console.log('   ⚠️  Uses "aws_auth_token" parameter (should be "token" for HLS)');
      } else {
        console.log('   ❌ No token parameter found');
      }
    }
    
    if (result.authorizedEmbedUrl) {
      console.log(`\n🎮 Embed URL Preview:`);
      console.log(`   ${result.authorizedEmbedUrl.substring(0, 100)}...`);
      
      // Check if it has the correct token parameter
      if (result.authorizedEmbedUrl.includes('?aws_auth_token=') || result.authorizedEmbedUrl.includes('&aws_auth_token=')) {
        console.log('   ✅ Uses correct "aws_auth_token" parameter for Player SDK URL');
      } else {
        console.log('   ❌ Missing aws_auth_token parameter');
      }
    }

    console.log('\n📋 Event Info:');
    console.log(`   ID: ${result.eventInfo.id}`);
    console.log(`   Title: ${result.eventInfo.title}`);
    console.log(`   Price: ${result.eventInfo.price} ${result.eventInfo.currency}`);

    console.log('\n📺 Channel Info:');
    console.log(`   ID: ${result.channelInfo.id}`);
    console.log(`   Name: ${result.channelInfo.name}`);
    console.log(`   ARN: ${result.channelInfo.channelArn}`);

    console.log('\n🎯 Test Result: SUCCESS! ✅');
    console.log('   The playback token generation is working correctly.');
    console.log('   Both HLS and Player SDK URLs are being generated properly.');

  } catch (error) {
    console.error('\n❌ Test Failed:');
    console.error(`   Error: ${error.message}`);
    
    if (error.message.includes('Can not find channel')) {
      console.error('\n🔍 Debugging Info:');
      console.error('   This error occurs when the event-channel relationship is missing.');
      console.error('   Check if the event exists and has a proper channel relationship.');
    }
  } finally {
    await neo4jService.close();
  }
}

testPlaybackTokenGeneration();

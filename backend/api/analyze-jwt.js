// Analyze the JWT token to see what's inside
const jwt = 'eyJhbGciOiJFUzM4NCIsInR5cCI6IkpXVCIsImtpZCI6IjFpdHN2OHd6a2l2WiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************.ITmfV0lTF_kiPVT2mqySXU4902SHJZ26nHV3JZcVRaj7f7y1Suf9L5pl6zOSob_dKYgc8eedUalMULWPW7YU4Bqr6PT8hXdcjaAvXxNYv1Rw-DWIu098tYGg7LItqDW4';

console.log('🔍 Analyzing JWT Token');
console.log('=====================\n');

// Decode the header
const [headerB64, payloadB64, signature] = jwt.split('.');

console.log('📋 JWT Header:');
const header = JSON.parse(Buffer.from(headerB64, 'base64').toString());
console.log(JSON.stringify(header, null, 2));

console.log('\n📋 JWT Payload:');
const payload = JSON.parse(Buffer.from(payloadB64, 'base64').toString());
console.log(JSON.stringify(payload, null, 2));

console.log('\n🔍 Analysis:');
console.log(`   Algorithm: ${header.alg}`);
console.log(`   Key ID: ${header.kid}`);
console.log(`   Channel ARN: ${payload['aws:channel-arn']}`);
console.log(`   Viewer ID: ${payload['aws:viewer-id']}`);
console.log(`   Issued At: ${new Date(payload.iat * 1000).toISOString()}`);
console.log(`   Expires At: ${new Date(payload.exp * 1000).toISOString()}`);
console.log(`   Access Control Allow Origin: ${payload['aws:access-control-allow-origin']}`);

// Check if token is expired
const now = Math.floor(Date.now() / 1000);
const isExpired = payload.exp < now;
console.log(`   Token Expired: ${isExpired ? 'YES' : 'NO'}`);

if (isExpired) {
  console.log(`   ⚠️  Token expired ${Math.floor((now - payload.exp) / 60)} minutes ago`);
} else {
  console.log(`   ✅ Token valid for ${Math.floor((payload.exp - now) / 60)} more minutes`);
}

// Extract channel ID from ARN
const channelArn = payload['aws:channel-arn'];
const channelId = channelArn.split('/').pop();
console.log(`\n🎯 Channel Information:`);
console.log(`   Full ARN: ${channelArn}`);
console.log(`   Channel ID: ${channelId}`);
console.log(`   Region: eu-west-1`);
console.log(`   Account: ************`);

// Check URL consistency
const urlChannelId = 'y6EL0pKPjFNj'; // From the URL
console.log(`\n🔗 URL Analysis:`);
console.log(`   URL Channel ID: ${urlChannelId}`);
console.log(`   Token Channel ID: ${channelId}`);
console.log(`   Match: ${urlChannelId === channelId ? 'YES ✅' : 'NO ❌'}`);

if (urlChannelId !== channelId) {
  console.log('\n❌ MISMATCH DETECTED!');
  console.log('   The channel ID in the URL does not match the channel ID in the JWT token.');
  console.log('   This could be why AWS IVS is returning "Can not find channel".');
} else {
  console.log('\n✅ Channel IDs match - the issue might be elsewhere.');
}

console.log('\n🎯 Possible Issues:');
console.log('   1. Channel might not exist in AWS IVS');
console.log('   2. Channel might be in a different region');
console.log('   3. Channel might be deleted or inactive');
console.log('   4. Playback URL format might be incorrect');
console.log('   5. Token might be using wrong key pair');

console.log('\n💡 Next Steps:');
console.log('   1. Verify the channel exists in AWS IVS console');
console.log('   2. Check if the channel is active and configured correctly');
console.log('   3. Verify the playback URL format matches AWS IVS expectations');

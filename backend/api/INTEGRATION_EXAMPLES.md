# Playback Authorization Integration Examples

## Frontend Integration Examples

### React Component for Video Player

```tsx
import React, { useState, useEffect } from 'react';

interface PlaybackData {
  requiresAuthorization: boolean;
  streamUrl?: string;
  embedUrl?: string;
  authorizedStreamUrl?: string;
  authorizedEmbedUrl?: string;
  token?: string;
  expiresAt?: string;
  message?: string;
}

interface VideoPlayerProps {
  eventId: string;
  userToken: string;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ eventId, userToken }) => {
  const [playbackData, setPlaybackData] = useState<PlaybackData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchPlaybackToken();
  }, [eventId]);

  const fetchPlaybackToken = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/events/${eventId}/playback-token`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to get playback authorization');
      }

      const data = await response.json();
      setPlaybackData(data);

      // Auto-refresh token before expiration for paid content
      if (data.requiresAuthorization && data.expiresAt) {
        scheduleTokenRefresh(data.expiresAt);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const scheduleTokenRefresh = (expiresAt: string) => {
    const expirationTime = new Date(expiresAt).getTime();
    const refreshTime = expirationTime - Date.now() - (5 * 60 * 1000); // 5 minutes before expiry

    if (refreshTime > 0) {
      setTimeout(fetchPlaybackToken, refreshTime);
    }
  };

  const getStreamUrl = () => {
    if (!playbackData) return null;

    // For authorized content, prioritize HLS stream URL (with token parameter)
    // over embed URL (with aws_auth_token parameter) for direct iframe playback
    return playbackData.requiresAuthorization
      ? (playbackData.authorizedStreamUrl || playbackData.authorizedEmbedUrl)
      : (playbackData.streamUrl || playbackData.embedUrl);
  };

  const getEmbedUrl = () => {
    if (!playbackData) return null;

    // For Player SDK integration, use embed URLs
    return playbackData.requiresAuthorization
      ? playbackData.authorizedEmbedUrl
      : playbackData.embedUrl;
  };

  if (loading) return <div>Loading video player...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!playbackData) return <div>No playback data available</div>;

  return (
    <div className="video-player-container">
      {playbackData.requiresAuthorization && (
        <div className="authorization-info">
          <p>🔒 This is premium content</p>
          {playbackData.expiresAt && (
            <p>Access expires: {new Date(playbackData.expiresAt).toLocaleString()}</p>
          )}
        </div>
      )}

      <iframe
        src={getEmbedUrl()}
        width="100%"
        height="400"
        frameBorder="0"
        allowFullScreen
        title="Video Player"
      />

      {!playbackData.requiresAuthorization && playbackData.message && (
        <p className="free-content-info">ℹ️ {playbackData.message}</p>
      )}
    </div>
  );
};

export default VideoPlayer;
```

### JavaScript IVS Player Integration

```javascript
import { IVSPlayer } from 'amazon-ivs-player';

class AuthorizedVideoPlayer {
  constructor(containerId, eventId, userToken) {
    this.containerId = containerId;
    this.eventId = eventId;
    this.userToken = userToken;
    this.player = null;
    this.refreshTimer = null;

    this.init();
  }

  async init() {
    try {
      // Get playback authorization
      const playbackData = await this.getPlaybackToken();

      // Create video element
      const videoElement = document.createElement('video');
      videoElement.setAttribute('controls', '');
      videoElement.setAttribute('playsinline', '');
      document.getElementById(this.containerId).appendChild(videoElement);

      // Initialize IVS player
      this.player = IVSPlayer.create();
      this.player.attachHTMLVideoElement(videoElement);

      // Set up event handlers
      this.setupEventHandlers();

      // Load the stream
      const streamUrl = playbackData.requiresAuthorization
        ? playbackData.authorizedStreamUrl
        : playbackData.streamUrl;

      if (streamUrl) {
        this.player.load(streamUrl);
      }

      // Schedule token refresh for paid content
      if (playbackData.requiresAuthorization && playbackData.expiresAt) {
        this.scheduleTokenRefresh(playbackData.expiresAt);
      }

    } catch (error) {
      console.error('Failed to initialize video player:', error);
      this.showError(error.message);
    }
  }

  async getPlaybackToken() {
    const response = await fetch(`/api/events/${this.eventId}/playback-token`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.userToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('Failed to get playback authorization');
    }

    return response.json();
  }

  scheduleTokenRefresh(expiresAt) {
    const expirationTime = new Date(expiresAt).getTime();
    const refreshTime = expirationTime - Date.now() - (5 * 60 * 1000); // 5 minutes before expiry

    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        try {
          console.log('Refreshing playback token...');
          const playbackData = await this.getPlaybackToken();

          if (playbackData.authorizedStreamUrl) {
            this.player.load(playbackData.authorizedStreamUrl);
          }

          // Schedule next refresh
          if (playbackData.expiresAt) {
            this.scheduleTokenRefresh(playbackData.expiresAt);
          }
        } catch (error) {
          console.error('Failed to refresh token:', error);
        }
      }, refreshTime);
    }
  }

  setupEventHandlers() {
    this.player.addEventListener(IVSPlayer.PlayerEventType.ERROR, (error) => {
      console.error('Player error:', error);
      this.showError('Video playback error occurred');
    });

    this.player.addEventListener(IVSPlayer.PlayerEventType.STATE_CHANGED, (state) => {
      console.log('Player state changed:', state);
    });
  }

  showError(message) {
    const container = document.getElementById(this.containerId);
    container.innerHTML = `<div class="error">Error: ${message}</div>`;
  }

  destroy() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    if (this.player) {
      this.player.delete();
    }
  }
}

// Usage
const player = new AuthorizedVideoPlayer('video-container', 'event-123', userToken);
```

## Backend Integration Examples

### Channel Configuration Script

```typescript
// scripts/configure-channel-playback.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { ChannelsService } from '../src/modules/channels/channels.service';

async function configureChannelPlayback() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const channelsService = app.get(ChannelsService);

  // IVS key pair from AWS CLI output
  const keyPairData = {
    keyPairId: 'keypair-12345678',
    publicKey: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`,
    privateKey: `-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...
-----END PRIVATE KEY-----`
  };

  try {
    // Configure playback authorization for a channel
    const result = await channelsService.updatePlaybackKeys(
      'channel-456',
      keyPairData.keyPairId,
      keyPairData.publicKey,
      keyPairData.privateKey
    );

    console.log('Playback authorization configured:', result);
  } catch (error) {
    console.error('Failed to configure playback authorization:', error);
  }

  await app.close();
}

configureChannelPlayback();
```

### Event Purchase Flow with Authorization

```typescript
// Example service showing complete purchase + authorization flow
@Injectable()
export class PurchaseService {
  constructor(
    private readonly eventsService: EventsService,
    private readonly paymentsService: PaymentsService,
  ) {}

  async purchaseEvent(userId: string, eventId: string, paymentData: any) {
    try {
      // 1. Process payment
      const payment = await this.paymentsService.processPayment(
        userId,
        eventId,
        paymentData
      );

      // 2. Record purchase in database
      const purchase = await this.eventsService.purchaseEvent(
        userId,
        eventId,
        payment.paymentIntentId
      );

      // 3. Generate initial playback token
      const playbackToken = await this.eventsService.generatePlaybackToken(
        eventId,
        userId
      );

      return {
        purchase,
        playbackToken,
        accessGranted: true,
        message: 'Purchase successful! You now have access to this event.'
      };
    } catch (error) {
      throw new Error(`Purchase failed: ${error.message}`);
    }
  }
}
```

## Testing Examples

### Unit Test for Playback Authorization

```typescript
// playback-auth.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { PlaybackAuthService } from './playback-auth.service';

describe('PlaybackAuthService', () => {
  let service: PlaybackAuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PlaybackAuthService],
    }).compile();

    service = module.get<PlaybackAuthService>(PlaybackAuthService);
  });

  describe('generatePlaybackToken', () => {
    it('should generate valid JWT token', () => {
      const channelArn = 'arn:aws:ivs:us-east-1:123456789:channel/test';
      const privateKey = `-----BEGIN PRIVATE KEY-----
MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC...
-----END PRIVATE KEY-----`;
      const keyPairId = 'test-keypair';
      const userId = 'user-123';

      const token = service.generatePlaybackToken(
        channelArn,
        privateKey,
        keyPairId,
        userId,
        60
      );

      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT format
    });
  });

  describe('requiresAuthorization', () => {
    it('should require authorization for paid content with keys', () => {
      const result = service.requiresAuthorization(19.99, true);
      expect(result).toBe(true);
    });

    it('should not require authorization for free content', () => {
      const result = service.requiresAuthorization(0, true);
      expect(result).toBe(false);
    });

    it('should not require authorization without playback keys', () => {
      const result = service.requiresAuthorization(19.99, false);
      expect(result).toBe(false);
    });
  });
});
```

### Integration Test for Event Authorization

```typescript
// events.controller.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Events Authorization (e2e)', () => {
  let app: INestApplication;
  let userToken: string;
  let eventId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Setup test data
    userToken = 'test-jwt-token';
    eventId = 'test-event-id';
  });

  it('/events/:id/playback-token (POST) - should return token for authorized user', () => {
    return request(app.getHttpServer())
      .post(`/events/${eventId}/playback-token`)
      .set('Authorization', `Bearer ${userToken}`)
      .send({ durationMinutes: 60 })
      .expect(201)
      .expect((res) => {
        expect(res.body).toHaveProperty('requiresAuthorization');
        if (res.body.requiresAuthorization) {
          expect(res.body).toHaveProperty('token');
          expect(res.body).toHaveProperty('authorizedStreamUrl');
        }
      });
  });

  it('/events/:id/playback-token (POST) - should reject unauthorized user', () => {
    return request(app.getHttpServer())
      .post(`/events/${eventId}/playback-token`)
      .set('Authorization', 'Bearer invalid-token')
      .send()
      .expect(401);
  });

  afterAll(async () => {
    await app.close();
  });
});
```

## AWS CLI Setup Script

```bash
#!/bin/bash
# setup-ivs-playback-auth.sh

echo "Setting up IVS Playback Authorization..."

# Create playback key pair
echo "Creating IVS playback key pair..."
KEY_PAIR_OUTPUT=$(aws ivs create-playback-key-pair \
  --name "video-stream-app-$(date +%s)" \
  --region us-east-1 \
  --output json)

if [ $? -eq 0 ]; then
  echo "✅ Key pair created successfully!"

  # Extract values
  KEY_PAIR_ID=$(echo $KEY_PAIR_OUTPUT | jq -r '.keyPair.arn' | cut -d'/' -f2)
  PUBLIC_KEY=$(echo $KEY_PAIR_OUTPUT | jq -r '.keyPair.publicKey')
  PRIVATE_KEY=$(echo $KEY_PAIR_OUTPUT | jq -r '.keyPair.privateKey')

  echo "📋 Key Pair Details:"
  echo "Key Pair ID: $KEY_PAIR_ID"
  echo "Public Key: (saved to public_key.pem)"
  echo "Private Key: (saved to private_key.pem)"

  # Save keys to files
  echo "$PUBLIC_KEY" > public_key.pem
  echo "$PRIVATE_KEY" > private_key.pem

  echo ""
  echo "🔧 Next steps:"
  echo "1. Store these keys securely in your channel configuration"
  echo "2. Use the API endpoint: PUT /api/channels/{channelId}/playback-keys"
  echo "3. Test with a paid event"

else
  echo "❌ Failed to create key pair"
  exit 1
fi
```

This implementation provides a complete solution for IVS playback authorization with practical examples for both frontend and backend integration.
import { Injectable, Inject, forwardRef, Logger } from '@nestjs/common';
import { Neo4jService } from '../../database/neo4j.service';
import { PricingPlansService } from '../pricing-plans/pricing-plans.service';
import { int } from 'neo4j-driver';
import { LikesService } from '../likes/likes.service';
import { ViewsService } from '../views/views.service';
import { IvsService } from '../streaming/ivs.service';
import { PlaybackAuthService } from '../streaming/playback-auth.service';
import { FilesService } from '../files/files.service';
import { FinancialsService } from '../financials/financials.service';

@Injectable()
export class EventsService {
  private readonly logger = new Logger(EventsService.name);

  constructor(
    private readonly neo4jService: Neo4jService,
    private readonly pricingPlansService: PricingPlansService,
    @Inject(forwardRef(() => LikesService))
    private readonly likesService: LikesService,
    @Inject(forwardRef(() => ViewsService))
    private readonly viewsService: ViewsService,
    private readonly ivsService: IvsService,
    private readonly playbackAuthService: PlaybackAuthService,
    private readonly filesService: FilesService,
    private readonly financialsService: FinancialsService,
  ) {}

  async create(channelId: string, data: any) {
    // Process the data before creating
    const processedData = { ...data };

    // Handle pricingPlanId - fetch pricing plan details and set price and currency
    if (processedData.pricingPlanId) {
      const pricingPlan = await this.pricingPlansService.findById(
        processedData.pricingPlanId,
      );
      if (pricingPlan) {
        processedData.price = pricingPlan.totalPrice;
        processedData.currency = pricingPlan.currency;
      }
    }

    // Handle videoAddress - convert object to JSON string if it exists
    if (
      processedData.videoAddress &&
      typeof processedData.videoAddress === 'object'
    ) {
      processedData.videoAddressJson = JSON.stringify(
        processedData.videoAddress,
      );
      delete processedData.videoAddress; // Remove the original object
    }

    const eventData = {
      ...processedData,
      id: `event-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'draft', // draft, published, upcoming, live, ended
    };

    // Dynamically build the CREATE clause based on available properties
    const properties = Object.keys(eventData)
      .filter((key) => key !== 'channel') // Exclude channel
      .map((key) => {
        // Handle datetime fields
        if (
          key === 'startTime' ||
          key === 'endTime' ||
          key === 'createdAt' ||
          key === 'updatedAt'
        ) {
          return `${key}: datetime($${key})`;
        }
        return `${key}: $${key}`;
      })
      .join(',\n        ');

    const result = await this.neo4jService.write(
      `
      MATCH (ch:Channel {id: $channelId})
      CREATE (e:Event {
        ${properties}
      })
      CREATE (ch)-[:HOSTS]->(e)
      RETURN e {
        .id,
        .title,
        .description,
        .price,
        .currency,
        .startTime,
        .endTime,
        .status,
        .createdAt,
        .updatedAt,
        .videoAddressJson,
        channel: ch {.id, .name}
      } AS event
      `,
      {
        channelId,
        ...eventData,
      },
    );

    // Process the result to convert videoAddressJson back to videoAddress
    const event = result.records[0]?.get('event');
    if (event && event.videoAddressJson) {
      try {
        event.videoAddress = JSON.parse(event.videoAddressJson);
        delete event.videoAddressJson; // Remove the JSON string from the response
      } catch (error) {
        console.error('Error parsing videoAddressJson:', error);
      }
    }

    return event;
  }

  async findAll(limit = 20, offset = 0, userId?: string) {
    const offsetInt = int(Math.floor(Number(offset)));
    const limitInt = int(Math.floor(Number(limit)));

    // Log the query parameters for debugging
    console.log(
      `Fetching events with limit: ${limitInt}, offset: ${offsetInt}`,
    );

    const result = await this.neo4jService.read(
      `
      MATCH (ch:Channel)-[:HOSTS]->(e:Event)
      // Remove the channel status filter to show all events
      OPTIONAL MATCH (:User)-[l:LIKED]->(e)
      OPTIONAL MATCH (:User)-[v:VIEWED]->(e)
      WITH e, ch, COUNT(l) AS likeCount, COUNT(v) AS viewCount
      RETURN e {
        .id,
        .title,
        .description,
        .imageUrl,
        .imageUrlKey,
        .price,
        .currency,
        .streamUrl,
        .startTime,
        .endTime,
        .status,
        .createdAt,
        .updatedAt,
        .videoAddressJson,
        .timezone,
        .forKids,
        .viewersOverEighteen,
        .category,
        .pricingPlanId,
        .duration,
        .tags,
        .anonymousViews,
        channel: ch {.id, .name, .imageUrl, .playbackUrl, .channelArn},
        likeCount: likeCount,
        viewCount: viewCount
      } AS event
      ORDER BY e.startTime DESC
      SKIP $offsetInt
      LIMIT $limitInt
      `,
      { limitInt, offsetInt },
    );

    // Log the number of events found
    console.log(`Found ${result.records.length} events`);

    // Log each event's status for debugging
    result.records.forEach((record) => {
      const event = record.get('event');
      console.log(
        `Event ${event.id} (${event.title}) - Status: ${event.status}, Type: ${typeof event.status}, Channel: ${event.channel?.name || 'Unknown'}`,
      );
    });

    // Log the number of published events with detailed type information
    const publishedEvents = result.records.filter((record) => {
      const event = record.get('event');
      const status = event.status;
      const statusType = typeof status;
      const statusString = String(status).toLowerCase();
      const isPublished = statusString === 'published';

      console.log(
        `Event ${event.id} - Status check: Original=${status}, Type=${statusType}, Lowercase=${statusString}, IsPublished=${isPublished}`,
      );

      return isPublished;
    });
    console.log(`Found ${publishedEvents.length} published events`);

    // Process each event to convert videoAddressJson back to videoAddress
    // and ensure status is one of the expected values
    const events = result.records.map((record) => {
      const event = record.get('event');

      // Process videoAddressJson
      if (event && event.videoAddressJson) {
        try {
          event.videoAddress = JSON.parse(event.videoAddressJson);
          delete event.videoAddressJson;
        } catch (error) {
          console.error('Error parsing videoAddressJson:', error);
        }
      }

      // Calculate total views (authenticated + anonymous)
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      const viewCount =
        typeof event.viewCount === 'bigint'
          ? Number(event.viewCount)
          : event.viewCount || 0;
      const anonymousViews =
        typeof event.anonymousViews === 'bigint'
          ? Number(event.anonymousViews)
          : event.anonymousViews || 0;

      // Log the types for debugging
      console.log(
        `Event ${event.id} - viewCount type: ${typeof viewCount}, anonymousViews type: ${typeof anonymousViews}`,
      );

      // Make sure both values are numbers before adding
      const viewCountNum = Number(viewCount);
      const anonymousViewsNum = Number(anonymousViews);

      // Set the views property
      event.views = viewCountNum + anonymousViewsNum;

      // Set likes property for frontend compatibility
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      event.likes =
        typeof event.likeCount === 'bigint'
          ? Number(event.likeCount)
          : event.likeCount || 0;

      // Ensure status is one of the expected values
      if (event.status) {
        // Convert status to lowercase for consistency
        const status = String(event.status).toLowerCase();

        // Map status to one of the expected values
        if (
          status === 'draft' ||
          status === 'published' ||
          status === 'live' ||
          status === 'ended' ||
          status === 'upcoming'
        ) {
          event.status = status;
        } else {
          // Default to 'draft' if status is not recognized
          console.log(
            `Converting unknown status '${status}' to 'draft' for event ${event.id}`,
          );
          event.status = 'draft';
        }
      } else {
        // Default to 'draft' if status is missing
        console.log(`Adding missing status as 'draft' for event ${event.id}`);
        event.status = 'draft';
      }

      return event;
    });

    // If userId is provided, check which events the user has liked
    if (userId) {
      const eventIds = events.map((event) => event.id);

      // Only check if there are events
      if (eventIds.length > 0) {
        const likeStatusResult = await this.neo4jService.read(
          `
          MATCH (u:User {id: $userId})-[l:LIKED]->(e:Event)
          WHERE e.id IN $eventIds
          RETURN e.id AS eventId
          `,
          { userId, eventIds },
        );

        const likedEventIds = likeStatusResult.records.map((record) =>
          record.get('eventId'),
        );

        // Mark events as liked or not
        events.forEach((event) => {
          event.isLiked = likedEventIds.includes(event.id);
        });
      }
    }

    return events;
  }

  async findById(id: string, userId?: string) {
    const result = await this.neo4jService.read(
      `
      MATCH (ch:Channel)-[:HOSTS]->(e:Event {id: $id})
      OPTIONAL MATCH (:User)-[l:LIKED]->(e)
      OPTIONAL MATCH (:User)-[v:VIEWED]->(e)
      WITH e, ch, COUNT(l) AS likeCount, COUNT(v) AS viewCount
      RETURN e {
        .id,
        .title,
        .description,
        .imageUrl,
        .imageUrlKey,
        .price,
        .currency,
        .streamUrl,
        .startTime,
        .endTime,
        .status,
        .createdAt,
        .updatedAt,
        .videoAddressJson,
        .timezone,
        .forKids,
        .viewersOverEighteen,
        .category,
        .pricingPlanId,
        .duration,
        .tags,
        .anonymousViews,
        channel: ch {.id, .name, .imageUrl, .playbackUrl, .channelArn},
        likeCount: likeCount,
        viewCount: viewCount
      } AS event
      `,
      { id },
    );

    // Process the result to convert videoAddressJson back to videoAddress
    const event = result.records[0]?.get('event');
    if (!event) return null;

    if (event.videoAddressJson) {
      try {
        event.videoAddress = JSON.parse(event.videoAddressJson);
        delete event.videoAddressJson; // Remove the JSON string from the response
      } catch (error) {
        console.error('Error parsing videoAddressJson:', error);
      }
    }

    // Calculate total views (authenticated + anonymous)
    // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
    const viewCount =
      typeof event.viewCount === 'bigint'
        ? Number(event.viewCount)
        : event.viewCount || 0;
    const anonymousViews =
      typeof event.anonymousViews === 'bigint'
        ? Number(event.anonymousViews)
        : event.anonymousViews || 0;

    // Log the types for debugging
    console.log(
      `Event ${event.id} - viewCount type: ${typeof viewCount}, anonymousViews type: ${typeof anonymousViews}`,
    );

    // Make sure both values are numbers before adding
    const viewCountNum = Number(viewCount);
    const anonymousViewsNum = Number(anonymousViews);

    // Set the views property
    event.views = viewCountNum + anonymousViewsNum;

    // Set likes property for frontend compatibility
    // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
    event.likes =
      typeof event.likeCount === 'bigint'
        ? Number(event.likeCount)
        : event.likeCount || 0;

    // Ensure status is one of the expected values
    if (event.status) {
      // Convert status to lowercase for consistency
      const status = String(event.status).toLowerCase();

      // Map status to one of the expected values
      if (
        status === 'draft' ||
        status === 'published' ||
        status === 'live' ||
        status === 'ended' ||
        status === 'upcoming'
      ) {
        event.status = status;
      } else {
        // Default to 'draft' if status is not recognized
        console.log(
          `Converting unknown status '${status}' to 'draft' for event ${event.id}`,
        );
        event.status = 'draft';
      }
    } else {
      // Default to 'draft' if status is missing
      console.log(`Adding missing status as 'draft' for event ${event.id}`);
      event.status = 'draft';
    }

    // If userId is provided, check if the user has liked the event
    if (userId) {
      const likeStatus = await this.likesService.checkUserLiked(userId, id);
      event.isLiked = likeStatus.liked;
    }

    return event;
  }

  async findByChannel(channelId: string, userId?: string) {
    const result = await this.neo4jService.read(
      `
      MATCH (ch:Channel {id: $channelId})-[:HOSTS]->(e:Event)
      OPTIONAL MATCH (:User)-[l:LIKED]->(e)
      OPTIONAL MATCH (:User)-[v:VIEWED]->(e)
      WITH e, ch, COUNT(l) AS likeCount, COUNT(v) AS viewCount
      RETURN e {
        .id,
        .title,
        .description,
        .imageUrl,
        .imageUrlKey,
        .price,
        .currency,
        .streamUrl,
        .startTime,
        .endTime,
        .status,
        .createdAt,
        .updatedAt,
        .videoAddressJson,
        .timezone,
        .forKids,
        .viewersOverEighteen,
        .category,
        .pricingPlanId,
        .duration,
        .tags,
        .anonymousViews,
        channel: ch {.id, .name, .imageUrl, .playbackUrl, .channelArn},
        likeCount: likeCount,
        viewCount: viewCount
      } AS event
      ORDER BY e.startTime DESC
      `,
      { channelId },
    );

    // Process each event to convert videoAddressJson back to videoAddress
    const events = result.records.map((record) => {
      const event = record.get('event');
      if (event && event.videoAddressJson) {
        try {
          event.videoAddress = JSON.parse(event.videoAddressJson);
          delete event.videoAddressJson;
        } catch (error) {
          console.error('Error parsing videoAddressJson:', error);
        }
      }

      // Calculate total views (authenticated + anonymous)
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      const viewCount =
        typeof event.viewCount === 'bigint'
          ? Number(event.viewCount)
          : event.viewCount || 0;
      const anonymousViews =
        typeof event.anonymousViews === 'bigint'
          ? Number(event.anonymousViews)
          : event.anonymousViews || 0;

      // Log the types for debugging
      console.log(
        `Event ${event.id} - viewCount type: ${typeof viewCount}, anonymousViews type: ${typeof anonymousViews}`,
      );

      // Make sure both values are numbers before adding
      const viewCountNum = Number(viewCount);
      const anonymousViewsNum = Number(anonymousViews);

      // Set the views property
      event.views = viewCountNum + anonymousViewsNum;

      // Set likes property for frontend compatibility
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      event.likes =
        typeof event.likeCount === 'bigint'
          ? Number(event.likeCount)
          : event.likeCount || 0;

      // Ensure status is one of the expected values
      if (event.status) {
        // Convert status to lowercase for consistency
        const status = String(event.status).toLowerCase();

        // Map status to one of the expected values
        if (
          status === 'draft' ||
          status === 'published' ||
          status === 'live' ||
          status === 'ended' ||
          status === 'upcoming'
        ) {
          event.status = status;
        } else {
          // Default to 'draft' if status is not recognized
          console.log(
            `Converting unknown status '${status}' to 'draft' for event ${event.id}`,
          );
          event.status = 'draft';
        }
      } else {
        // Default to 'draft' if status is missing
        console.log(`Adding missing status as 'draft' for event ${event.id}`);
        event.status = 'draft';
      }

      return event;
    });

    // If userId is provided, check which events the user has liked
    if (userId) {
      const eventIds = events.map((event) => event.id);

      // Only check if there are events
      if (eventIds.length > 0) {
        const likeStatusResult = await this.neo4jService.read(
          `
          MATCH (u:User {id: $userId})-[l:LIKED]->(e:Event)
          WHERE e.id IN $eventIds
          RETURN e.id AS eventId
          `,
          { userId, eventIds },
        );

        const likedEventIds = likeStatusResult.records.map((record) =>
          record.get('eventId'),
        );

        // Mark events as liked or not
        events.forEach((event) => {
          event.isLiked = likedEventIds.includes(event.id);
        });
      }
    }

    return events;
  }

  async update(id: string, data: any) {
    // Process the data before updating
    const processedData = { ...data };

    // Handle videoAddress - convert object to JSON string if it exists
    if (
      processedData.videoAddress &&
      typeof processedData.videoAddress === 'object'
    ) {
      processedData.videoAddressJson = JSON.stringify(
        processedData.videoAddress,
      );
      delete processedData.videoAddress; // Remove the original object
    }

    const updateData = {
      ...processedData,
      updatedAt: new Date().toISOString(),
    };

    const setClause = Object.keys(updateData)
      .filter((key) => key !== 'channel') // Exclude channel from update
      .map((key) => {
        // Handle datetime fields
        if (
          key === 'startTime' ||
          key === 'endTime' ||
          key === 'createdAt' ||
          key === 'updatedAt'
        ) {
          return `e.${key} = datetime($${key})`;
        }
        return `e.${key} = $${key}`;
      })
      .join(', ');

    const result = await this.neo4jService.write(
      `
      MATCH (ch:Channel)-[:HOSTS]->(e:Event {id: $id})
      SET ${setClause}
      RETURN e {
        .id,
        .title,
        .description,
        .imageUrl,
        .imageUrlKey,
        .price,
        .currency,
        .streamUrl,
        .startTime,
        .endTime,
        .status,
        .createdAt,
        .updatedAt,
        .videoAddressJson,
        .timezone,
        .forKids,
        .viewersOverEighteen,
        .category,
        .pricingPlanId,
        .duration,
        .tags,
        channel: ch {.id, .name, .imageUrl}
      } AS event
      `,
      { id, ...updateData },
    );

    // Process the result to convert videoAddressJson back to videoAddress
    const event = result.records[0]?.get('event');
    if (!event) return null;

    if (event.videoAddressJson) {
      try {
        event.videoAddress = JSON.parse(event.videoAddressJson);
        delete event.videoAddressJson; // Remove the JSON string from the response
      } catch (error) {
        console.error('Error parsing videoAddressJson:', error);
      }
    }

    // Ensure status is one of the expected values
    if (event.status) {
      // Convert status to lowercase for consistency
      const status = String(event.status).toLowerCase();

      // Map status to one of the expected values
      if (
        status === 'draft' ||
        status === 'published' ||
        status === 'live' ||
        status === 'ended' ||
        status === 'upcoming'
      ) {
        event.status = status;
      } else {
        // Default to 'draft' if status is not recognized
        console.log(
          `Converting unknown status '${status}' to 'draft' for event ${event.id}`,
        );
        event.status = 'draft';
      }
    } else {
      // Default to 'draft' if status is missing
      console.log(`Adding missing status as 'draft' for event ${event.id}`);
      event.status = 'draft';
    }

    return event;
  }

  /**
   * Update event thumbnail with file replacement
   * @param id Event ID
   * @param file New thumbnail file buffer
   * @param filename Original filename
   * @returns Updated event data
   */
  async updateThumbnail(id: string, file: Buffer, filename: string) {
    // Get current event to check for existing thumbnail key
    const currentEvent = await this.findById(id);
    const oldThumbnailKey = currentEvent?.imageUrlKey;

    // Upload new thumbnail (and delete old one if exists)
    const uploadResult = await this.filesService.replaceImageByKey(
      file,
      filename,
      oldThumbnailKey,
    );

    // Update event with new thumbnail URL and file key
    return this.update(id, {
      imageUrl: uploadResult.url,
      imageUrlKey: uploadResult.fileKey,
    });
  }

  async setLive(id: string) {
    return this.update(id, { status: 'live' });
  }

  async setEnded(id: string) {
    return this.update(id, { status: 'ended' });
  }

  async publishEvent(id: string) {
    console.log(`Publishing event ${id} - validating required fields first`);

    // First, fetch the event to validate required fields
    const event = await this.findById(id);
    if (!event) {
      throw new Error('Event not found');
    }

    // Validate all required fields for publishing
    const missingFields: string[] = [];

    if (!event.title || event.title.trim() === '') {
      missingFields.push('title');
    }

    if (!event.description || event.description.trim() === '') {
      missingFields.push('description');
    }

    if (!event.imageUrl || event.imageUrl.trim() === '') {
      missingFields.push('thumbnail');
    }

    if (!event.category || event.category.trim() === '') {
      missingFields.push('category');
    }

    if (!event.pricingPlanId || event.pricingPlanId.trim() === '') {
      missingFields.push('pricing plan');
    }

    if (!event.startTime) {
      missingFields.push('start time');
    }

    if (!event.endTime) {
      missingFields.push('end time');
    }

    if (!event.timezone || event.timezone.trim() === '') {
      missingFields.push('timezone');
    }

    if (missingFields.length > 0) {
      const fieldsList = missingFields.join(', ');
      throw new Error(
        `Cannot publish event. Missing required fields: ${fieldsList}`,
      );
    }

    console.log(
      `All required fields validated for event ${id} - setting status to 'published'`,
    );
    const result = await this.update(id, { status: 'published' });
    console.log(`Event ${id} published status result:`, result.status);
    return result;
  }

  async delete(id: string) {
    try {
      // Get event to check for files to clean up
      const event = await this.findById(id);

      // Clean up uploaded files
      if (event && event.imageUrlKey) {
        try {
          await this.filesService.deleteFileByKey(event.imageUrlKey);
          this.logger.log(`Deleted event thumbnail: ${event.imageUrlKey}`);
        } catch (error) {
          this.logger.warn(
            `Failed to delete event thumbnail: ${event.imageUrlKey}`,
            error,
          );
        }
      }

      await this.neo4jService.write(
        `
        MATCH (e:Event {id: $id})
        DETACH DELETE e
        `,
        { id },
      );

      return { id, deleted: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to delete event: ${errorMessage}`);
      throw error instanceof Error ? error : new Error(errorMessage);
    }
  }

  /**
   * Check if a user owns a specific event (through channel ownership)
   * @param userId User ID
   * @param eventId Event ID
   * @returns Boolean indicating if the user owns the event
   */
  async isOwner(userId: string, eventId: string): Promise<boolean> {
    const result = await this.neo4jService.read(
      `
      MATCH (u:User {id: $userId})-[:OWNS]->(c:Channel)-[:HOSTS]->(e:Event {id: $eventId})
      RETURN COUNT(e) > 0 AS isOwner
      `,
      { userId, eventId },
    );

    return result.records[0]?.get('isOwner') || false;
  }

  async purchaseEvent(
    userId: string,
    eventId: string,
    paymentIntentId: string,
    paymentMethod: string = 'stripe',
  ) {
    console.log(
      `Processing purchase for user ${userId}, event ${eventId}, payment ${paymentIntentId}, method ${paymentMethod}`,
    );

    // First get the event details to calculate commission
    const eventResult = await this.neo4jService.read(
      `MATCH (e:Event {id: $eventId})
       RETURN e.price AS price, e.currency AS currency, e.duration AS duration, e.startTime AS startTime, e.endTime AS endTime`,
      { eventId },
    );

    const price = eventResult.records[0]?.get('price') || 0;
    const currency = eventResult.records[0]?.get('currency') || 'usd';
    const duration = eventResult.records[0]?.get('duration') || null;
    const startTime = eventResult.records[0]?.get('startTime');
    const endTime = eventResult.records[0]?.get('endTime');

    // Calculate event duration in hours
    let eventDurationHours = 0;
    if (duration) {
      // If duration is stored in minutes, convert to hours
      eventDurationHours = duration / 60;
    } else if (startTime && endTime) {
      // Calculate from start and end time
      const start = new Date(startTime);
      const end = new Date(endTime);
      eventDurationHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
    } else {
      // Default to 2 hours if no duration info
      eventDurationHours = 2;
    }

    // Get current participant count (including this purchase)
    const participantResult = await this.neo4jService.read(
      `MATCH (e:Event {id: $eventId})<-[:PURCHASED]-(:User)
       RETURN count(*) as currentParticipants`,
      { eventId }
    );

    const currentParticipants = participantResult.records[0]?.get('currentParticipants')?.toNumber() || 0;
    const totalParticipants = currentParticipants + 1; // Including this new purchase

    // Calculate commission using the financials service, including all fees
    const commission = this.financialsService.calculateCommission(
      price,
      paymentMethod,
      eventDurationHours,
      totalParticipants,
      'ADVANCED_HD' // Default to ADVANCED_HD for AWS IVS
    );

    // Create the purchase with commission details
    const result = await this.neo4jService.write(
      `
      MATCH (u:User {id: $userId})
      MATCH (e:Event {id: $eventId})
      CREATE (u)-[p:PURCHASED {
        id: randomUUID(),
        paymentIntentId: $paymentIntentId,
        amount: $price,
        netAmount: $netAmount,
        processingFee: $processingFee,
        streamingCharges: $streamingCharges,
        participantHours: $participantHours,
        participantCount: $participantCount,
        totalFees: $totalFees,
        currency: $currency,
        platformCommission: $platformCommission,
        creatorEarnings: $creatorEarnings,
        paymentMethod: $paymentMethod,
        status: 'completed',
        purchasedAt: datetime(),
        paidOutAt: null,
        payoutReference: null,
        payoutMethod: null
      }]->(e)
      RETURN p {
        .id,
        .paymentIntentId,
        .amount,
        .netAmount,
        .processingFee,
        .streamingCharges,
        .totalFees,
        .currency,
        .platformCommission,
        .creatorEarnings,
        .paymentMethod,
        .status,
        .purchasedAt,
        user: u {.id, .email},
        event: e {.id, .title}
      } AS purchase
      `,
      {
        userId,
        eventId,
        paymentIntentId,
        price,
        netAmount: commission.netAmount,
        processingFee: commission.processingFee,
        streamingCharges: commission.streamingCharges,
        participantHours: commission.participantHours,
        participantCount: totalParticipants,
        totalFees: commission.totalFees,
        currency,
        platformCommission: commission.platformCommission,
        creatorEarnings: commission.creatorEarnings,
        paymentMethod,
      },
    );

    const purchase = result.records[0]?.get('purchase');
    console.log('Purchase recorded with commission and processing fee:', purchase);

    return purchase;
  }

  /**
   * Find events for the homepage with specific statuses (published, live, ended)
   * @param limit Maximum number of events to return
   * @param offset Number of events to skip
   * @param userId Optional User ID to check if the user has liked each event
   * @returns Array of events
   */
  async findHomepageEvents(limit = 20, offset = 0, userId?: string) {
    // Convert to integers
    const offsetInt = int(Math.floor(Number(offset)));
    const limitInt = int(Math.floor(Number(limit)));

    // Log the query parameters for debugging
    console.log(
      `Fetching homepage events with limit: ${limitInt}, offset: ${offsetInt}`,
    );

    const result = await this.neo4jService.read(
      `
      MATCH (ch:Channel)-[:HOSTS]->(e:Event)
      WHERE toLower(e.status) IN ['published', 'live', 'ended']
      OPTIONAL MATCH (:User)-[l:LIKED]->(e)
      OPTIONAL MATCH (:User)-[v:VIEWED]->(e)
      WITH e, ch, COUNT(l) AS likeCount, COUNT(v) AS viewCount
      RETURN e {
        .id,
        .title,
        .description,
        .imageUrl,
        .imageUrlKey,
        .price,
        .currency,
        .streamUrl,
        .startTime,
        .endTime,
        .status,
        .createdAt,
        .updatedAt,
        .videoAddressJson,
        .timezone,
        .forKids,
        .viewersOverEighteen,
        .category,
        .pricingPlanId,
        .duration,
        .tags,
        .anonymousViews,
        channel: ch {.id, .name, .imageUrl, .playbackUrl, .channelArn},
        likeCount: likeCount,
        viewCount: viewCount
      } AS event
      ORDER BY
        CASE WHEN toLower(e.status) = 'live' THEN 0 ELSE 1 END, // Live events first
        e.startTime DESC                                        // Then by start time
      SKIP $offsetInt
      LIMIT $limitInt
      `,
      { limitInt, offsetInt },
    );

    // Log the number of events found
    console.log(`Found ${result.records.length} homepage events`);

    // Log each event's status for debugging
    result.records.forEach((record) => {
      const event = record.get('event');
      console.log(
        `Homepage event ${event.id} (${event.title}) - Status: ${event.status}, Channel: ${event.channel?.name || 'Unknown'}`,
      );
    });

    // Process each event to convert videoAddressJson back to videoAddress
    // and ensure status is one of the expected values
    const events = result.records.map((record) => {
      const event = record.get('event');

      // Process videoAddressJson
      if (event && event.videoAddressJson) {
        try {
          event.videoAddress = JSON.parse(event.videoAddressJson);
          delete event.videoAddressJson;
        } catch (error) {
          console.error('Error parsing videoAddressJson:', error);
        }
      }

      // Calculate total views (authenticated + anonymous)
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      const viewCount =
        typeof event.viewCount === 'bigint'
          ? Number(event.viewCount)
          : event.viewCount || 0;
      const anonymousViews =
        typeof event.anonymousViews === 'bigint'
          ? Number(event.anonymousViews)
          : event.anonymousViews || 0;

      // Log the types for debugging
      console.log(
        `Event ${event.id} - viewCount type: ${typeof viewCount}, anonymousViews type: ${typeof anonymousViews}`,
      );

      // Make sure both values are numbers before adding
      const viewCountNum = Number(viewCount);
      const anonymousViewsNum = Number(anonymousViews);

      // Set the views property
      event.views = viewCountNum + anonymousViewsNum;

      // Set likes property for frontend compatibility
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      if (
        event.likeCount &&
        typeof event.likeCount === 'object' &&
        'low' in event.likeCount
      ) {
        // Neo4j Integer object
        event.likes = event.likeCount.low;
      } else if (typeof event.likeCount === 'bigint') {
        // BigInt
        event.likes = Number(event.likeCount);
      } else {
        // Regular number or null/undefined
        event.likes = event.likeCount || 0;
      }

      // Ensure status is one of the expected values
      if (event.status) {
        // Convert status to lowercase for consistency
        const status = String(event.status).toLowerCase();

        // Map status to one of the expected values
        if (
          status === 'draft' ||
          status === 'published' ||
          status === 'live' ||
          status === 'ended' ||
          status === 'upcoming'
        ) {
          event.status = status;
        } else {
          // Default to 'draft' if status is not recognized
          console.log(
            `Converting unknown status '${status}' to 'draft' for event ${event.id}`,
          );
          event.status = 'draft';
        }
      } else {
        // Default to 'draft' if status is missing
        console.log(`Adding missing status as 'draft' for event ${event.id}`);
        event.status = 'draft';
      }

      return event;
    });

    // If userId is provided, check which events the user has liked
    if (userId) {
      const eventIds = events.map((event) => event.id);

      // Only check if there are events
      if (eventIds.length > 0) {
        const likeStatusResult = await this.neo4jService.read(
          `
          MATCH (u:User {id: $userId})-[l:LIKED]->(e:Event)
          WHERE e.id IN $eventIds
          RETURN e.id AS eventId
          `,
          { userId, eventIds },
        );

        const likedEventIds = likeStatusResult.records.map((record) =>
          record.get('eventId'),
        );

        // Mark events as liked or not
        events.forEach((event) => {
          event.isLiked = likedEventIds.includes(event.id);
        });
      }
    }

    return events;
  }

  async getUserPurchases(userId: string) {
    const result = await this.neo4jService.read(
      `
      MATCH (u:User {id: $userId})-[p:PURCHASED]->(e:Event)
      MATCH (ch:Channel)-[:HOSTS]->(e)
      RETURN p {
        .id,
        .paymentIntentId,
        .amount,
        .currency,
        .status,
        .purchasedAt,
        event: e {
          .id,
          .title,
          .description,
          .imageUrl,
          .imageUrlKey,
          .startTime,
          .endTime,
          .status,
          .videoAddressJson,
          .timezone,
          .forKids,
          .viewersOverEighteen,
          .category,
          .pricingPlanId,
          .duration,
          .tags,
          channel: ch {.id, .name, .imageUrl}
        }
      } AS purchase
      ORDER BY p.purchasedAt DESC
      `,
      { userId },
    );

    // Process each purchase to convert videoAddressJson back to videoAddress
    return result.records.map((record) => {
      const purchase = record.get('purchase');
      if (purchase && purchase.event && purchase.event.videoAddressJson) {
        try {
          purchase.event.videoAddress = JSON.parse(
            purchase.event.videoAddressJson,
          );
          delete purchase.event.videoAddressJson;
        } catch (error) {
          console.error('Error parsing videoAddressJson:', error);
        }
      }
      return purchase;
    });
  }

  async checkUserAccess(userId: string, eventId: string) {
    console.log(`Checking access for user ${userId} to event ${eventId}`);

    // First check if the event is free
    const eventResult = await this.neo4jService.read(
      `
      MATCH (e:Event {id: $eventId})
      RETURN e.price AS price
      `,
      { eventId },
    );

    const price = eventResult.records[0]?.get('price');
    console.log(`Event ${eventId} price: ${price}`);

    // If the event is free, grant access
    if (price === 0 || price === null || price === undefined) {
      console.log(
        `Event ${eventId} is free, granting access to user ${userId}`,
      );
      return true;
    }

    // Otherwise, check if the user has purchased the event
    const result = await this.neo4jService.read(
      `
      MATCH (u:User {id: $userId})-[p:PURCHASED]->(e:Event {id: $eventId})
      RETURN count(p) > 0 AS hasAccess
      `,
      { userId, eventId },
    );

    const hasAccess = result.records[0]?.get('hasAccess') || false;
    console.log(
      `User ${userId} has ${hasAccess ? 'access' : 'no access'} to event ${eventId}`,
    );

    return hasAccess;
  }

  /**
   * Find events for a channel with specific statuses (published, live, ended)
   * @param channelId Channel ID to find events for
   * @param limit Maximum number of events to return
   * @param offset Number of events to skip
   * @param userId Optional User ID to check if the user has liked each event
   * @returns Object with events array and pagination metadata
   */
  async findPublicChannelEvents(
    channelId: string,
    limit = 20,
    offset = 0,
    userId?: string,
  ) {
    // Convert to integers
    const offsetInt = int(Math.floor(Number(offset)));
    const limitInt = int(Math.floor(Number(limit)));

    // Log the query parameters for debugging
    console.log(
      `Fetching public channel events with limit: ${limitInt}, offset: ${offsetInt}, channelId: ${channelId}`,
    );

    // First get the total count
    const countResult = await this.neo4jService.read(
      `
      MATCH (ch:Channel {id: $channelId})-[:HOSTS]->(e:Event)
      WHERE toLower(e.status) IN ['published', 'live', 'ended']
      RETURN COUNT(e) AS total
      `,
      { channelId },
    );

    const total =
      countResult.records.length > 0
        ? countResult.records[0].get('total').toNumber()
        : 0;

    // Then get the paginated events
    const result = await this.neo4jService.read(
      `
      MATCH (ch:Channel {id: $channelId})-[:HOSTS]->(e:Event)
      WHERE toLower(e.status) IN ['published', 'live', 'ended']
      OPTIONAL MATCH (:User)-[l:LIKED]->(e)
      OPTIONAL MATCH (:User)-[v:VIEWED]->(e)
      WITH e, ch, COUNT(l) AS likeCount, COUNT(v) AS viewCount
      RETURN e {
        .id,
        .title,
        .description,
        .imageUrl,
        .imageUrlKey,
        .price,
        .currency,
        .streamUrl,
        .startTime,
        .endTime,
        .status,
        .createdAt,
        .updatedAt,
        .videoAddressJson,
        .timezone,
        .forKids,
        .viewersOverEighteen,
        .category,
        .pricingPlanId,
        .duration,
        .tags,
        .anonymousViews,
        channel: ch {.id, .name, .imageUrl, .playbackUrl, .channelArn},
        likeCount: likeCount,
        viewCount: viewCount
      } AS event
      ORDER BY
        CASE WHEN toLower(e.status) = 'live' THEN 0 ELSE 1 END, // Live events first
        e.startTime DESC                                        // Then by start time
      SKIP $offsetInt
      LIMIT $limitInt
      `,
      { channelId, limitInt, offsetInt },
    );

    // Log the number of events found
    console.log(
      `Found ${result.records.length} public channel events out of ${total} total`,
    );

    // Process each event to convert videoAddressJson back to videoAddress
    // and ensure status is one of the expected values
    const events = result.records.map((record) => {
      const event = record.get('event');

      // Process videoAddressJson
      if (event && event.videoAddressJson) {
        try {
          event.videoAddress = JSON.parse(event.videoAddressJson);
          delete event.videoAddressJson;
        } catch (error) {
          console.error('Error parsing videoAddressJson:', error);
        }
      }

      // Calculate total views (authenticated + anonymous)
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      const viewCount =
        typeof event.viewCount === 'bigint'
          ? Number(event.viewCount)
          : event.viewCount || 0;
      const anonymousViews =
        typeof event.anonymousViews === 'bigint'
          ? Number(event.anonymousViews)
          : event.anonymousViews || 0;

      // Make sure both values are numbers before adding
      const viewCountNum = Number(viewCount);
      const anonymousViewsNum = Number(anonymousViews);

      // Set the views property
      event.views = viewCountNum + anonymousViewsNum;

      // Set likes property for frontend compatibility
      // Convert Neo4j integers to JavaScript numbers to avoid BigInt mixing issues
      if (
        event.likeCount &&
        typeof event.likeCount === 'object' &&
        'low' in event.likeCount
      ) {
        // Neo4j Integer object
        event.likes = event.likeCount.low;
      } else if (typeof event.likeCount === 'bigint') {
        // BigInt
        event.likes = Number(event.likeCount);
      } else {
        // Regular number or null/undefined
        event.likes = event.likeCount || 0;
      }

      // Ensure status is one of the expected values
      if (event.status) {
        // Convert status to lowercase for consistency
        const status = String(event.status).toLowerCase();

        // Map status to one of the expected values
        if (
          status === 'draft' ||
          status === 'published' ||
          status === 'live' ||
          status === 'ended' ||
          status === 'upcoming'
        ) {
          event.status = status;
        } else {
          // Default to 'draft' if status is not recognized
          console.log(
            `Converting unknown status '${status}' to 'draft' for event ${event.id}`,
          );
          event.status = 'draft';
        }
      } else {
        // Default to 'draft' if status is missing
        console.log(`Adding missing status as 'draft' for event ${event.id}`);
        event.status = 'draft';
      }

      return event;
    });

    // If userId is provided, check which events the user has liked
    if (userId) {
      const eventIds = events.map((event) => event.id);

      // Only check if there are events
      if (eventIds.length > 0) {
        const likeStatusResult = await this.neo4jService.read(
          `
          MATCH (u:User {id: $userId})-[l:LIKED]->(e:Event)
          WHERE e.id IN $eventIds
          RETURN e.id AS eventId
          `,
          { userId, eventIds },
        );

        const likedEventIds = likeStatusResult.records.map((record) =>
          record.get('eventId'),
        );

        // Mark events as liked or not
        events.forEach((event) => {
          event.isLiked = likedEventIds.includes(event.id);
        });
      }
    }

    return {
      events,
      total,
      limit: limitInt,
      offset: offsetInt,
    };
  }

  /**
   * Helper method to get the current time in a specific timezone
   * This tells us what time it is RIGHT NOW in the given timezone
   */
  private getCurrentTimeInTimezone(timezone: string): Date {
    try {
      // Get the current time components in the target timezone
      const now = new Date();
      const formatter = new Intl.DateTimeFormat('en-CA', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });

      const parts = formatter.formatToParts(now);
      const year = parseInt(parts.find((p) => p.type === 'year')?.value || '0');
      const month =
        parseInt(parts.find((p) => p.type === 'month')?.value || '0') - 1; // Month is 0-indexed
      const day = parseInt(parts.find((p) => p.type === 'day')?.value || '0');
      const hour = parseInt(parts.find((p) => p.type === 'hour')?.value || '0');
      const minute = parseInt(
        parts.find((p) => p.type === 'minute')?.value || '0',
      );
      const second = parseInt(
        parts.find((p) => p.type === 'second')?.value || '0',
      );

      // Create a new Date object representing the current time in the target timezone
      return new Date(year, month, day, hour, minute, second);
    } catch (error) {
      console.error('Error getting current time in timezone:', error);
      // Fallback to current server time if conversion fails
      return new Date();
    }
  }

  /**
   * Helper method to convert a stored date to a specific timezone
   * Uses Intl.DateTimeFormat for reliable timezone conversion
   */
  private convertToTimezone(date: Date, timezone: string): Date {
    try {
      // Get the date components in the target timezone
      const formatter = new Intl.DateTimeFormat('en-CA', {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });

      const parts = formatter.formatToParts(date);
      const year = parseInt(parts.find((p) => p.type === 'year')?.value || '0');
      const month =
        parseInt(parts.find((p) => p.type === 'month')?.value || '0') - 1; // Month is 0-indexed
      const day = parseInt(parts.find((p) => p.type === 'day')?.value || '0');
      const hour = parseInt(parts.find((p) => p.type === 'hour')?.value || '0');
      const minute = parseInt(
        parts.find((p) => p.type === 'minute')?.value || '0',
      );
      const second = parseInt(
        parts.find((p) => p.type === 'second')?.value || '0',
      );

      // Create a new Date object with the converted components
      return new Date(year, month, day, hour, minute, second);
    } catch (error) {
      console.error('Error converting date to timezone:', error);
      // Fallback to original date if conversion fails
      return date;
    }
  }

  /**
   * Get the real-time stream status for an event
   * This checks if the channel's AWS IVS stream is actually live
   * Only returns stream URLs if user has access to the event
   */
  async getStreamStatus(eventId: string, userId?: string) {
    try {
      // Get the event and its channel information, including price for access check
      const result = await this.neo4jService.read(
        `
        MATCH (ch:Channel)-[:HOSTS]->(e:Event {id: $eventId})
        RETURN e {
          .id,
          .title,
          .status,
          .streamUrl,
          .startTime,
          .endTime,
          .price,
          .currency,
          .timezone
        } AS event,
        ch {
          .id,
          .name,
          .channelArn,
          .playbackUrl
        } AS channel
        `,
        { eventId },
      );

      if (!result.records.length) {
        return {
          eventId,
          found: false,
          error: 'Event not found',
        };
      }

      const event = result.records[0].get('event');
      const channel = result.records[0].get('channel');

      // Check if user has access to this event (for paid events)
      let hasAccess = true;
      if (userId && event.price && event.price > 0) {
        hasAccess = await this.checkUserAccess(userId, eventId);
      }

      // Additional check: Only allow stream access during the event's scheduled time
      // This prevents users from watching future events' streams even if they have channel access
      // Use timezone-aware comparison to handle events in different timezones
      const now = new Date();
      const eventStartTime = new Date(event.startTime);
      const eventEndTime = new Date(event.endTime);

      // Convert current time to event's timezone for proper comparison
      // This checks if it's currently within the event's scheduled time in the event's timezone
      let isEventCurrentlyScheduled = false;
      try {
        if (event.timezone) {
          // Get what time it is RIGHT NOW in the event's timezone
          const nowInEventTimezone = this.getCurrentTimeInTimezone(
            event.timezone,
          );

          // Event times are stored in UTC, so we need to convert them to the event's timezone
          const eventStartInEventTimezone = this.convertToTimezone(
            eventStartTime,
            event.timezone,
          );
          const eventEndInEventTimezone = this.convertToTimezone(
            eventEndTime,
            event.timezone,
          );

          isEventCurrentlyScheduled =
            nowInEventTimezone >= eventStartInEventTimezone &&
            nowInEventTimezone <= eventEndInEventTimezone;
        } else {
          // Fallback to UTC comparison if no timezone is specified
          isEventCurrentlyScheduled =
            now >= eventStartTime && now <= eventEndTime;
        }
      } catch (error) {
        console.error(
          'Error in timezone conversion for event',
          eventId,
          ':',
          error,
        );
        // Fallback to UTC comparison if timezone conversion fails
        isEventCurrentlyScheduled =
          now >= eventStartTime && now <= eventEndTime;
      }

      // For live streams, only allow access if:
      // 1. User has purchased this specific event, AND
      // 2. The event is currently scheduled to be live (within its time window)
      const allowStreamAccess =
        hasAccess && (isEventCurrentlyScheduled || event.status === 'live');

      // Check if the channel has AWS IVS configuration
      if (!channel.channelArn) {
        return {
          eventId,
          found: true,
          hasAccess,
          allowStreamAccess,
          event: {
            id: event.id,
            title: event.title,
            status: event.status,
            streamUrl: allowStreamAccess ? event.streamUrl : null, // only return if user has stream access
            startTime: event.startTime,
            endTime: event.endTime,
            price: event.price,
            currency: event.currency,
          },
          channel: {
            id: channel.id,
            name: channel.name,
            playbackUrl: allowStreamAccess ? channel.playbackUrl : null, // only return if user has stream access
          },
          streamLive: false,
          isEventCurrentlyScheduled,
          error: 'Channel not configured for streaming (no AWS IVS channel)',
        };
      }

      // Check if channel has playback authorization enabled
      let hasPlaybackKeys = !!(channel.playbackKeyPairId && channel.playbackPrivateKey);

      // If channel doesn't have keys, check if shared keys are available
      if (!hasPlaybackKeys) {
        const sharedKeys = this.ivsService.getSharedPlaybackKeys();
        if (sharedKeys) {
          hasPlaybackKeys = true;
          this.logger.log(`Channel ${channel.id} will use shared playback keys for stream status authorization`);
        }
      }

      if (!hasPlaybackKeys && channel.channelArn) {
        // Check if the AWS IVS channel has authorization enabled
        try {
          const isAuthEnabled = await this.ivsService.isChannelAuthorizationEnabled(channel.channelArn);
          if (!isAuthEnabled) {
            // Channel doesn't have authorization enabled - enable it for pay-per-view
            this.logger.warn(`Channel ${channel.id} doesn't have authorization enabled. Enabling authorization for pay-per-view platform.`);
            await this.ivsService.enableChannelAuthorization(channel.channelArn);
          }

          if (!hasPlaybackKeys) {
            // No playback keys configured - this will prevent stream access
            this.logger.error(`Channel ${channel.id} has no playback keys configured. Stream access will be denied.`);
          }
        } catch (error) {
          this.logger.warn(`Failed to check/enable channel authorization: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      const requiresAuthorization = this.playbackAuthService.requiresAuthorization(
        event.price,
        hasPlaybackKeys
      );

      // Don't check AWS IVS for past events since we use one playback URL per channel
      let isStreamLive = false;

      // Use timezone-aware comparison to check if event has ended
      let isEventEnded = false;
      try {
        if (event.timezone) {
          // Get what time it is RIGHT NOW in the event's timezone
          const nowInEventTimezone = this.getCurrentTimeInTimezone(
            event.timezone,
          );
          const eventEndInEventTimezone = this.convertToTimezone(
            eventEndTime,
            event.timezone,
          );

          isEventEnded = nowInEventTimezone > eventEndInEventTimezone;
        } else {
          // Fallback to UTC comparison if no timezone is specified
          isEventEnded = now > eventEndTime;
        }
      } catch (error) {
        console.error(
          'Error in timezone conversion for event end check',
          eventId,
          ':',
          error,
        );
        // Fallback to UTC comparison if timezone conversion fails
        isEventEnded = now > eventEndTime;
      }

      if (!isEventEnded) {
        // Only check AWS IVS stream status for current/upcoming events
        isStreamLive = await this.ivsService.isLive(channel.channelArn);
      }

      // Determine the effective stream URL (only if user has stream access)
      let effectiveStreamUrl: string | null = null;
      let embedUrl: string | null = null;
      let authorizedStreamUrl: string | null = null;
      let authorizedEmbedUrl: string | null = null;

      // For pay-per-view platform, require playback keys for all content
      const hasValidStreamAccess = allowStreamAccess && hasPlaybackKeys;

      if (!hasPlaybackKeys && allowStreamAccess) {
        this.logger.error(`Stream access denied for event ${eventId}: No playback keys configured for channel ${channel.id}`);
      }

      if (hasValidStreamAccess) {
        effectiveStreamUrl = event.streamUrl;
        if (!effectiveStreamUrl && channel.playbackUrl) {
          // Use channel's playback URL if event doesn't have a specific stream URL
          effectiveStreamUrl = channel.playbackUrl;
        }

        // Generate embed URL for IVS player to handle CORS properly
        if (channel.channelArn) {
          embedUrl = `https://player.live-video.net/1.23.0/amazon-ivs-player.html?channel=${encodeURIComponent(channel.channelArn)}`;
        }

        // If authorization is required, generate authorized URLs
        if (requiresAuthorization && hasPlaybackKeys) {
          try {
            // Calculate token duration
            const eventDurationHours = event.duration ? event.duration / 60 :
              (event.endTime && event.startTime ?
                (new Date(event.endTime).getTime() - new Date(event.startTime).getTime()) / (1000 * 60 * 60) :
                2);

            const tokenDuration = this.playbackAuthService.getTokenDuration(event.price, eventDurationHours);

            // Generate the playback token using shared keys or database keys
            let privateKey = channel.playbackPrivateKey;
            let keyPairId = channel.playbackKeyPairId;

            // If channel doesn't have keys, try to use shared keys
            if (!privateKey || !keyPairId) {
              const sharedKeys = this.ivsService.getSharedPlaybackKeys();
              if (sharedKeys) {
                privateKey = sharedKeys.privateKey;
                keyPairId = sharedKeys.keyPairId;
                this.logger.log(`Using shared playback keys for stream status token generation for event: ${eventId}`);
              } else {
                throw new Error('No valid playback keys available for token generation. Configure shared keys or channel-specific keys.');
              }
            }

            const token = this.playbackAuthService.generateSecurePlaybackToken(
              channel.channelArn,
              privateKey,
              keyPairId,
              userId || 'anonymous',
              tokenDuration
            );

            // Build authorized URLs
            if (effectiveStreamUrl) {
              authorizedStreamUrl = this.playbackAuthService.buildAuthorizedPlaybackUrl(effectiveStreamUrl, token);
            }

            authorizedEmbedUrl = this.playbackAuthService.buildAuthorizedEmbedUrl(channel.channelArn, token);

          } catch (error) {
            this.logger.error('Error generating playback token for stream status:', error);
            // Fall back to non-authorized URLs if token generation fails
          }
        }
      }

      return {
        eventId,
        found: true,
        hasAccess,
        allowStreamAccess: hasValidStreamAccess,
        isEventCurrentlyScheduled,
        requiresAuthorization,
        event: {
          id: event.id,
          title: event.title,
          status: event.status,
          streamUrl: effectiveStreamUrl, // null if user doesn't have stream access
          embedUrl: embedUrl, // IVS embed URL for proper CORS handling
          authorizedStreamUrl: authorizedStreamUrl, // Authorized stream URL with token
          authorizedEmbedUrl: authorizedEmbedUrl, // Authorized embed URL with token
          startTime: event.startTime,
          endTime: event.endTime,
          price: event.price,
          currency: event.currency,
        },
        channel: {
          id: channel.id,
          name: channel.name,
          channelArn: hasValidStreamAccess ? channel.channelArn : null, // hide sensitive info if no stream access
          playbackUrl: hasValidStreamAccess ? channel.playbackUrl : null, // hide sensitive info if no stream access
          hasPlaybackKeys: hasPlaybackKeys,
        },
        streamLive: isStreamLive,
        shouldAutoSetLive:
          hasValidStreamAccess && isStreamLive && event.status !== 'live',
        shouldAutoSetEnded:
          hasValidStreamAccess && !isStreamLive && event.status === 'live',
      };
    } catch (error) {
      console.error('Error checking stream status:', error);
      return {
        eventId,
        found: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Auto-update event status based on actual stream status
   * This can be called periodically or triggered by webhooks
   */
  async syncEventWithStreamStatus(eventId: string) {
    try {
      const streamStatus = await this.getStreamStatus(eventId);

      if (!streamStatus.found || streamStatus.error) {
        return streamStatus;
      }

      let updated = false;
      let newStatus = streamStatus.event?.status;

      // Auto-set to live if stream is live but event is not
      if (streamStatus.shouldAutoSetLive) {
        await this.setLive(eventId);
        newStatus = 'live';
        updated = true;
      }

      // Auto-set to ended if stream is not live but event is live
      else if (streamStatus.shouldAutoSetEnded) {
        await this.setEnded(eventId);
        newStatus = 'ended';
        updated = true;
      }

      return {
        ...streamStatus,
        updated,
        newStatus,
      };
    } catch (error) {
      console.error('Error syncing event with stream status:', error);
      return {
        eventId,
        found: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get the current or upcoming event for a channel based on stream time
   * This handles cases where:
   * 1. Stream starts before the scheduled event time (grace period)
   * 2. Multiple events are scheduled on the same day
   * 3. No event is currently scheduled
   * 4. Events in different timezones
   */
  async getCurrentEventByChannel(channelId: string, streamTime?: string) {
    const now = streamTime ? new Date(streamTime) : new Date();

    // Define a grace period (e.g., 30 minutes before event start)
    const gracePeriodMinutes = 30;

    try {
      // First, try to find a currently live event
      const liveResult = await this.neo4jService.read(
        `
        MATCH (ch:Channel {id: $channelId})-[:HOSTS]->(e:Event)
        WHERE toLower(e.status) = 'live'
        RETURN e {
          .id,
          .title,
          .startTime,
          .endTime,
          .status,
          .timezone
        } AS event
        LIMIT 1
        `,
        { channelId },
      );

      if (liveResult.records.length > 0) {
        return liveResult.records[0].get('event');
      }

      // Get all published/upcoming events for the channel
      const allEventsResult = await this.neo4jService.read(
        `
        MATCH (ch:Channel {id: $channelId})-[:HOSTS]->(e:Event)
        WHERE toLower(e.status) IN ['published', 'upcoming']
        AND e.startTime IS NOT NULL
        AND e.endTime IS NOT NULL
        RETURN e {
          .id,
          .title,
          .startTime,
          .endTime,
          .status,
          .timezone
        } AS event
        ORDER BY e.startTime
        `,
        { channelId },
      );

      if (allEventsResult.records.length === 0) {
        throw new Error('No current or upcoming events found for this channel');
      }

      // Process each event with timezone awareness
      const events = allEventsResult.records.map(record => {
        const event = record.get('event');
        const eventStartTime = new Date(event.startTime);
        const eventEndTime = new Date(event.endTime);

        let priority = 3; // Default to past event
        let isWithinWindow = false;

        if (event.timezone) {
          // Timezone-aware comparison
          const nowInEventTz = this.getCurrentTimeInTimezone(event.timezone);
          const eventStartInTz = this.convertToTimezone(eventStartTime, event.timezone);
          const eventEndInTz = this.convertToTimezone(eventEndTime, event.timezone);

          // Calculate grace time in event timezone
          const graceTimeInTz = new Date(nowInEventTz.getTime() - gracePeriodMinutes * 60 * 1000);

          if (nowInEventTz >= eventStartInTz && nowInEventTz <= eventEndInTz) {
            priority = 0; // Currently scheduled
            isWithinWindow = true;
          } else if (graceTimeInTz <= eventStartInTz && eventStartInTz <= nowInEventTz) {
            priority = 1; // Within grace period
            isWithinWindow = true;
          } else if (eventStartInTz > nowInEventTz) {
            priority = 2; // Future event
          }
        } else {
          // UTC comparison for events without timezone
          const graceTime = new Date(now.getTime() - gracePeriodMinutes * 60 * 1000);

          if (now >= eventStartTime && now <= eventEndTime) {
            priority = 0; // Currently scheduled
            isWithinWindow = true;
          } else if (graceTime <= eventStartTime && eventStartTime <= now) {
            priority = 1; // Within grace period
            isWithinWindow = true;
          } else if (eventStartTime > now) {
            priority = 2; // Future event
          }
        }

        return {
          ...event,
          priority,
          isWithinWindow
        };
      });

      // Filter out past events and sort by priority
      const eligibleEvents = events
        .filter(e => e.priority < 3)
        .sort((a, b) => {
          // First sort by priority
          if (a.priority !== b.priority) {
            return a.priority - b.priority;
          }
          // Then by start time (closer events first)
          return new Date(a.startTime).getTime() - new Date(b.startTime).getTime();
        });

      if (eligibleEvents.length === 0) {
        throw new Error('No current or upcoming events found for this channel');
      }

      const selectedEvent = eligibleEvents[0];

      // Remove internal fields before returning
      delete selectedEvent.priority;
      delete selectedEvent.isWithinWindow;

      return selectedEvent;
    } catch (error) {
      console.error('Error getting current event for channel:', error);
      throw error;
    }
  }

  /**
   * Update stream status from Lambda function
   * This is called by the IVS event handler Lambda with system_admin authentication
   */
  async updateStreamStatus(updateDto: {
    channelId: string;
    streamId: string;
    status: string;
    timestamp: string;
    eventName?: string;
    warning?: string;
    error?: string;
    expectedEndTime?: string;
    actualEndTime?: string;
    eventId?: string;
    viewerCount?: number;
    startTime?: string;
    endTime?: string;
  }) {
    const { channelId, status, eventId, timestamp } = updateDto;

    try {
      // If eventId is provided, use it directly
      let targetEventId = eventId;

      // Otherwise, find the current event for the channel
      if (!targetEventId) {
        try {
          const currentEvent = await this.getCurrentEventByChannel(channelId, timestamp);
          targetEventId = currentEvent.id;
        } catch (error) {
          console.error('No active event found for channel:', channelId);
          // Log the stream status update even if no event is found
          return {
            success: false,
            message: 'No active event found for channel',
            channelId,
            status,
            timestamp,
          };
        }
      }

      // Map Lambda status to our event status
      let eventStatus = status;
      switch (status) {
        case 'LIVE':
          eventStatus = 'live';
          break;
        case 'ENDED':
          eventStatus = 'ended';
          break;
        case 'TECHNICAL_ISSUE':
        case 'FAILED':
          // Don't change the event status for technical issues
          // Just log the issue
          console.error(`Stream issue for event ${targetEventId}:`, updateDto);

          // You might want to store this in a separate stream_issues table
          // or send notifications to admins
          return {
            success: true,
            eventId: targetEventId,
            status: eventStatus,
            warning: updateDto.warning,
            error: updateDto.error,
            timestamp,
          };
      }

      // Update the event status
      const updatedEvent = await this.update(targetEventId??'', {
        status: eventStatus,
        lastStreamUpdate: timestamp,
      });

      return {
        success: true,
        eventId: targetEventId,
        status: eventStatus,
        event: updatedEvent,
        timestamp,
      };
    } catch (error) {
      console.error('Error updating stream status:', error);
      throw error;
    }
  }

  /**
   * Generate playback authorization token for paid events
   * @param eventId Event ID
   * @param userId User ID requesting the token
   * @param durationMinutes Optional token duration (calculated automatically if not provided)
   * @returns Playback token and related information
   */
  async generatePlaybackToken(
    eventId: string,
    userId: string,
    durationMinutes?: number
  ) {
    try {
      // Get event and channel information
      const result = await this.neo4jService.read(
        `
        MATCH (ch:Channel)-[:HOSTS]->(e:Event {id: $eventId})
        RETURN e {
          .id,
          .title,
          .price,
          .currency,
          .startTime,
          .endTime,
          .duration
        } AS event,
        ch {
          .id,
          .name,
          .channelArn,
          .playbackUrl,
          .playbackKeyPairId,
          .playbackPublicKey,
          .playbackPrivateKey
        } AS channel
        `,
        { eventId },
      );

      if (!result.records.length) {
        throw new Error('Event not found');
      }

      const event = result.records[0].get('event');
      const channel = result.records[0].get('channel');

      // Check if user has access to this event
      const hasAccess = await this.checkUserAccess(userId, eventId);
      if (!hasAccess) {
        throw new Error('User does not have access to this event');
      }

      // Check if channel has playback keys configured for authorization
      let hasPlaybackKeys = !!(channel.playbackKeyPairId && channel.playbackPrivateKey);

      // If channel doesn't have keys, check if shared keys are available
      if (!hasPlaybackKeys) {
        const sharedKeys = this.ivsService.getSharedPlaybackKeys();
        if (sharedKeys) {
          hasPlaybackKeys = true;
          this.logger.log(`Channel ${channel.id} will use shared playback keys for authorization`);
        }
      }

      if (!hasPlaybackKeys && channel.channelArn) {
        // Check if the AWS IVS channel has authorization enabled
        try {
          const isAuthEnabled = await this.ivsService.isChannelAuthorizationEnabled(channel.channelArn);
          if (!isAuthEnabled) {
            // Channel doesn't have authorization enabled - enable it for pay-per-view
            this.logger.warn(`Channel ${channel.id} doesn't have authorization enabled. Enabling authorization for pay-per-view platform.`);
            await this.ivsService.enableChannelAuthorization(channel.channelArn);
          }
        } catch (error) {
          this.logger.warn(`Failed to check/enable channel authorization: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      if (!channel.channelArn) {
        throw new Error('Channel not configured for streaming (no AWS IVS channel)');
      }

      if (!hasPlaybackKeys) {
        // No playback keys configured - deny access for pay-per-view platform
        throw new Error('Channel not configured for playback authorization. All content requires payment and authorization. Configure shared keys or channel-specific keys.');
      }

      // Check if authorization is required (should always be true for pay-per-view)
      const requiresAuth = this.playbackAuthService.requiresAuthorization(
        event.price,
        hasPlaybackKeys
      );

      if (!requiresAuth) {
        // This should not happen in pay-per-view platform
        throw new Error('Authorization configuration error. All content requires authorization.');
      }

      // Calculate token duration if not provided
      let tokenDuration = durationMinutes;
      if (!tokenDuration) {
        const eventDurationHours = event.duration ? event.duration / 60 :
          (event.endTime && event.startTime ?
            (new Date(event.endTime).getTime() - new Date(event.startTime).getTime()) / (1000 * 60 * 60) :
            2);

        tokenDuration = this.playbackAuthService.getTokenDuration(event.price, eventDurationHours);
      }

      // Generate the playback token using shared keys or database keys
      let privateKey = channel.playbackPrivateKey;
      let keyPairId = channel.playbackKeyPairId;

      // If channel doesn't have keys, try to use shared keys
      if (!privateKey || !keyPairId) {
        const sharedKeys = this.ivsService.getSharedPlaybackKeys();
        if (sharedKeys) {
          privateKey = sharedKeys.privateKey;
          keyPairId = sharedKeys.keyPairId;
          this.logger.log(`Using shared playback keys for token generation for event: ${eventId}`);
        } else {
          throw new Error('No valid playback keys available for token generation. Configure shared keys or channel-specific keys.');
        }
      }

      const token = this.playbackAuthService.generateSecurePlaybackToken(
        channel.channelArn,
        privateKey,
        keyPairId,
        userId,
        tokenDuration
      );

      // Build authorized URLs
      const authorizedStreamUrl = channel.playbackUrl ?
        this.playbackAuthService.buildAuthorizedPlaybackUrl(channel.playbackUrl, token) :
        null;

      const authorizedEmbedUrl = this.playbackAuthService.buildAuthorizedEmbedUrl(
        channel.channelArn,
        token
      );

      return {
        requiresAuthorization: true,
        token,
        authorizedStreamUrl,
        authorizedEmbedUrl,
        expiresInMinutes: tokenDuration,
        expiresAt: new Date(Date.now() + tokenDuration * 60 * 1000).toISOString(),
        eventInfo: {
          id: event.id,
          title: event.title,
          price: event.price,
          currency: event.currency
        },
        channelInfo: {
          id: channel.id,
          name: channel.name,
          channelArn: channel.channelArn
        }
      };
    } catch (error) {
      this.logger.error('Error generating playback token:', error);
      throw error;
    }
  }
}

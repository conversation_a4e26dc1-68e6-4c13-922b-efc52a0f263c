import { Injectable, Logger } from '@nestjs/common';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';

interface PlaybackTokenPayload {
  'aws:channel-arn': string;
  'aws:viewer-id'?: string;
  'aws:access-control-allow-origin'?: string;
  'aws:strict-origin-enforcement'?: boolean;
  'aws:single-use-uuid'?: string;
  'aws:viewer-session-version'?: number;
  'aws:maximum-resolution'?: 'SD' | 'HD' | 'FULL_HD';
  exp: number;
  iat: number;
}

interface PlaybackKeyPair {
  publicKey: string;
  privateKey: string;
  keyPairId: string;
}

@Injectable()
export class PlaybackAuthService {
  private readonly logger = new Logger(PlaybackAuthService.name);

  /**
   * Generate a signed JWT token for IVS playback authorization
   * @param channelArn AWS IVS Channel ARN
   * @param privateKey Private key from IVS playback key pair
   * @param keyPairId Key pair ID from IVS
   * @param userId Optional user ID for tracking (viewer-id)
   * @param durationMinutes Token validity duration in minutes (default: 60, max 10 if viewer-id is used)
   * @param options Additional token options
   * @returns Signed JWT token
   */
  generatePlaybackToken(
    channelArn: string,
    privateKey: string,
    keyPairId: string,
    userId?: string,
    durationMinutes: number = 60,
    options?: {
      allowedOrigins?: string[];
      strictOriginEnforcement?: boolean;
      singleUse?: boolean;
      maxResolution?: 'SD' | 'HD' | 'FULL_HD';
      viewerSessionVersion?: number;
    }
  ): string {
    try {
      const now = Math.floor(Date.now() / 1000);

      // AWS IVS limitation: when using viewer-id, max exp is 10 minutes
      let effectiveDuration = durationMinutes;
      if (userId && durationMinutes > 10) {
        effectiveDuration = 10;
        this.logger.warn(`Token duration reduced to 10 minutes due to viewer-id usage (was ${durationMinutes} minutes)`);
      }

      const expirationTime = now + (effectiveDuration * 60);

      const payload: PlaybackTokenPayload = {
        'aws:channel-arn': channelArn,
        exp: expirationTime,
        iat: now,
      };

      // Add viewer ID if provided (AWS standard claim)
      if (userId) {
        payload['aws:viewer-id'] = userId;

        // Add viewer session version if provided
        if (options?.viewerSessionVersion !== undefined) {
          payload['aws:viewer-session-version'] = options.viewerSessionVersion;
        }
      }

      // Add origin restrictions if provided
      if (options?.allowedOrigins && options.allowedOrigins.length > 0) {
        payload['aws:access-control-allow-origin'] = options.allowedOrigins.join(',');

        if (options.strictOriginEnforcement) {
          payload['aws:strict-origin-enforcement'] = true;
        }
      }

      // Add single-use UUID if requested
      if (options?.singleUse) {
        payload['aws:single-use-uuid'] = crypto.randomUUID();
      }

      // Add maximum resolution if specified
      if (options?.maxResolution) {
        payload['aws:maximum-resolution'] = options.maxResolution;
      }

      // Sign the token with the ECDSA private key (P-384 curve)
      // Use 'kid' in header as per AWS documentation
      const token = jwt.sign(payload, privateKey, {
        algorithm: 'ES384',  // ECDSA with P-384 curve
        header: {
          alg: 'ES384',
          typ: 'JWT',
          kid: keyPairId  // AWS requires 'kid' in header
        }
      });

      // Debug: Log token details for troubleshooting
      this.logger.log(`Generated playback token for channel ${channelArn}, expires in ${effectiveDuration} minutes${userId ? ` for viewer ${userId}` : ''}`);
      this.logger.debug(`Token key pair ID: ${keyPairId}`);
      this.logger.debug(`Token payload: ${JSON.stringify(payload, null, 2)}`);

      // Decode and log the token header for verification
      try {
        const tokenParts = token.split('.');
        const header = JSON.parse(Buffer.from(tokenParts[0], 'base64url').toString());
        this.logger.debug(`Token header: ${JSON.stringify(header, null, 2)}`);
      } catch (decodeError) {
        this.logger.warn('Could not decode token header for debugging');
      }

      return token;
    } catch (error) {
      this.logger.error('Error generating playback token:', error);
      throw new Error('Failed to generate playback token');
    }
  }

  /**
   * Verify a playback token (for testing purposes)
   * @param token JWT token to verify
   * @param publicKey Public key from IVS playback key pair
   * @returns Decoded token payload if valid
   */
  verifyPlaybackToken(token: string, publicKey: string): PlaybackTokenPayload {
    try {
      const decoded = jwt.verify(token, publicKey, {
        algorithms: ['ES384'],  // ECDSA with P-384 curve
      }) as PlaybackTokenPayload;

      // Log token details for debugging
      this.logger.debug(`Token verified successfully for channel: ${decoded['aws:channel-arn']}`);
      if (decoded['aws:viewer-id']) {
        this.logger.debug(`Token viewer ID: ${decoded['aws:viewer-id']}`);
      }
      if (decoded['aws:access-control-allow-origin']) {
        this.logger.debug(`Token allowed origins: ${decoded['aws:access-control-allow-origin']}`);
      }

      return decoded;
    } catch (error) {
      this.logger.error('Error verifying playback token:', error);
      throw new Error('Invalid playback token');
    }
  }

  /**
   * Generate a playback key pair for IVS (for documentation purposes)
   * In production, this should be done through AWS CLI or console
   * @returns Key pair information
   */
  generateKeyPairInstructions(): {
    awsCliCommand: string;
    description: string;
  } {
    return {
      description: 'To create a playback key pair for IVS, use the AWS CLI:',
      awsCliCommand: `aws ivs create-playback-key-pair --name "your-app-playback-keys" --region us-east-1`,
    };
  }

  /**
   * Build authorization URL with token for IVS player
   * @param playbackUrl Original IVS playback URL
   * @param token JWT authorization token
   * @returns URL with authorization token
   */
  buildAuthorizedPlaybackUrl(playbackUrl: string, token: string): string {
    try {
      const url = new URL(playbackUrl);

      // Check if this is an HLS playback URL (contains .m3u8)
      if (playbackUrl.includes('.m3u8')) {
        // For HLS URLs, use 'token' parameter (Method 1)
        url.searchParams.set('token', token);
        this.logger.debug('Using HLS playback URL with token parameter');
      } else {
        // For other URLs, use 'aws_auth_token' parameter (Method 2)
        url.searchParams.set('aws_auth_token', token);
        this.logger.debug('Using player SDK URL with aws_auth_token parameter');
      }

      return url.toString();
    } catch (error) {
      this.logger.error('Error building authorized playback URL:', error);
      throw new Error('Failed to build authorized playback URL');
    }
  }

  /**
   * Generate embed URL with playback authorization
   * @param channelArn IVS Channel ARN
   * @param token JWT authorization token
   * @returns IVS player embed URL with authorization
   */
  buildAuthorizedEmbedUrl(channelArn: string, token: string): string {
    try {
      const baseEmbedUrl = 'https://player.live-video.net/1.23.0/amazon-ivs-player.html';
      const url = new URL(baseEmbedUrl);
      url.searchParams.set('channel', channelArn);
      url.searchParams.set('aws_auth_token', token);
      return url.toString();
    } catch (error) {
      this.logger.error('Error building authorized embed URL:', error);
      throw new Error('Failed to build authorized embed URL');
    }
  }

  /**
   * Build HLS playback URL with token parameter (Method 1)
   * @param playbackUrl Original HLS playback URL (.m3u8)
   * @param token JWT authorization token
   * @returns HLS URL with token parameter
   */
  buildAuthorizedHlsUrl(playbackUrl: string, token: string): string {
    try {
      if (!playbackUrl.includes('.m3u8')) {
        throw new Error('Invalid HLS URL: must contain .m3u8');
      }

      const url = new URL(playbackUrl);
      url.searchParams.set('token', token);
      this.logger.debug(`Built authorized HLS URL with token parameter`);
      return url.toString();
    } catch (error) {
      this.logger.error('Error building authorized HLS URL:', error);
      throw new Error('Failed to build authorized HLS URL');
    }
  }

  /**
   * Check if a channel requires playback authorization
   * @param price Event price (not used - all content requires authorization)
   * @param hasPlaybackKeys Whether channel has playback keys configured
   * @returns Boolean indicating if authorization is required
   */
  requiresAuthorization(_price: number, hasPlaybackKeys: boolean): boolean {
    // Require authorization for ALL content when playback keys are configured
    // This is a pay-per-view platform - no public content
    return hasPlaybackKeys;
  }

  /**
   * Generate a secure playback token with platform-specific defaults
   * @param channelArn AWS IVS Channel ARN
   * @param privateKey Private key from IVS playback key pair
   * @param keyPairId Key pair ID from IVS
   * @param userId User ID for tracking
   * @param durationMinutes Token validity duration in minutes
   * @param allowedOrigins Optional array of allowed origins for CORS
   * @returns Signed JWT token with security features
   */
  generateSecurePlaybackToken(
    channelArn: string,
    privateKey: string,
    keyPairId: string,
    userId: string,
    durationMinutes: number,
    allowedOrigins?: string[]
  ): string {
    return this.generatePlaybackToken(
      channelArn,
      privateKey,
      keyPairId,
      userId,
      durationMinutes,
      {
        allowedOrigins: allowedOrigins || ['*'], // Allow all origins by default
        strictOriginEnforcement: false, // Don't enforce strict origin for now
        singleUse: false, // Allow token reuse for better UX
        viewerSessionVersion: Math.floor(Date.now() / 1000), // Use current timestamp as session version
      }
    );
  }

  /**
   * Get token duration based on event duration
   * @param price Event price (not used - all content is pay-per-view)
   * @param eventDurationHours Event duration in hours
   * @returns Token duration in minutes (respects AWS limits)
   */
  getTokenDuration(_price: number, eventDurationHours: number = 2): number {
    // All events are pay-per-view: token should last for the entire event + buffer
    const bufferMinutes = 30;
    const calculatedDuration = Math.max(60, (eventDurationHours * 60) + bufferMinutes);

    // AWS IVS limitation: when using viewer-id, max exp is 10 minutes
    // For longer events, we'll need to implement token refresh
    return Math.min(calculatedDuration, 10 * 60); // Max 10 hours or 600 minutes
  }

  /**
   * Validate playback key pair format
   * @param publicKey Public key string
   * @param privateKey Private key string
   * @returns Validation result
   */
  validateKeyPair(publicKey: string, privateKey: string): {
    isValid: boolean;
    error?: string;
  } {
    try {
      // Check if keys are in correct PEM format
      if (!publicKey.includes('-----BEGIN PUBLIC KEY-----') ||
          !publicKey.includes('-----END PUBLIC KEY-----')) {
        return { isValid: false, error: 'Invalid public key format. Must be PEM format.' };
      }

      if ((!privateKey.includes('-----BEGIN PRIVATE KEY-----') ||
          !privateKey.includes('-----END PRIVATE KEY-----')) &&
          (!privateKey.includes('-----BEGIN EC PRIVATE KEY-----') ||
          !privateKey.includes('-----END EC PRIVATE KEY-----'))) {
        return { isValid: false, error: 'Invalid private key format. Must be PEM format (PKCS#8 or SEC1).' };
      }

      // Test key pair by generating and verifying a test token
      const testPayload: PlaybackTokenPayload = {
        'aws:channel-arn': 'arn:aws:ivs:us-east-1:123456789:channel/test',
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000),
      };

      const testToken = jwt.sign(testPayload, privateKey, {
        algorithm: 'ES384',  // ECDSA with P-384 curve
        header: {
          alg: 'ES384',
          typ: 'JWT',
          kid: 'test-key-id'  // AWS requires 'kid' in header
        }
      });

      jwt.verify(testToken, publicKey, { algorithms: ['ES384'] });

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: `Key pair validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}
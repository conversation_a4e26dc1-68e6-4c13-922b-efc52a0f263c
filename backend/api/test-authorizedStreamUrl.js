const { PlaybackAuthService } = require('./dist/modules/streaming/playback-auth.service');
const { IvsService } = require('./dist/modules/streaming/ivs.service');

// Load environment variables
require('dotenv').config();

async function testAuthorizedStreamUrl() {
  console.log('🎬 Testing authorizedStreamUrl Generation');
  console.log('========================================\n');

  try {
    // Initialize services
    const playbackAuthService = new PlaybackAuthService();
    const ivsService = new IvsService();
    
    // Initialize IVS service with shared keys
    console.log('🔑 Initializing IVS service with shared playback keys...');
    await ivsService.initializeSharedPlaybackKeys();
    console.log('✅ IVS service initialized successfully\n');

    // Test data - using the known working event
    const testData = {
      eventId: 'event-1748546908092',
      userId: 'test-user-123',
      channelArn: 'arn:aws:ivs:eu-west-1:223358806502:channel/y6EL0pKPjFNj',
      playbackUrl: 'https://b37c565f6d790a14a0e78afaa6808a80.eu-west-1.playback.live-video.net/api/video/v1/aws.ivs.eu-west-1.223358806502.channel.y6EL0pKPjFNj.m3u8'
    };

    console.log('📋 Test Data:');
    console.log(`   Event ID: ${testData.eventId}`);
    console.log(`   User ID: ${testData.userId}`);
    console.log(`   Channel ARN: ${testData.channelArn}`);
    console.log(`   Playback URL: ${testData.playbackUrl}\n`);

    // Generate JWT token for AWS IVS
    console.log('🔐 Generating JWT token for AWS IVS...');
    const token = await playbackAuthService.generateToken(
      testData.channelArn,
      testData.userId,
      10 // 10 minutes
    );
    console.log(`✅ JWT token generated (${token.length} characters)\n`);

    // Test HLS URL generation (should use 'token' parameter)
    console.log('🎥 Testing HLS URL generation:');
    const authorizedStreamUrl = ivsService.buildAuthorizedHlsUrl(testData.playbackUrl, token);
    console.log(`   Generated URL: ${authorizedStreamUrl}\n`);

    // Verify the URL format
    console.log('🔍 URL Analysis:');
    const url = new URL(authorizedStreamUrl);
    
    if (url.pathname.endsWith('.m3u8')) {
      console.log('   ✅ Correct HLS format (.m3u8)');
    } else {
      console.log('   ❌ Not an HLS URL');
    }

    if (url.searchParams.has('token')) {
      console.log('   ✅ Uses correct "token" parameter for HLS');
      console.log(`   📝 Token parameter length: ${url.searchParams.get('token').length} characters`);
    } else if (url.searchParams.has('aws_auth_token')) {
      console.log('   ⚠️  Uses "aws_auth_token" parameter (should be "token" for HLS)');
    } else {
      console.log('   ❌ No token parameter found');
    }

    // Test Player SDK URL generation (should use 'aws_auth_token' parameter)
    console.log('\n🎮 Testing Player SDK URL generation:');
    const embedUrl = 'https://player.live-video.net/1.23.0/amazon-ivs-player.html';
    const authorizedEmbedUrl = ivsService.buildAuthorizedPlayerUrl(embedUrl, testData.channelArn, token);
    console.log(`   Generated URL: ${authorizedEmbedUrl}\n`);

    // Verify the embed URL format
    console.log('🔍 Embed URL Analysis:');
    const embedUrlObj = new URL(authorizedEmbedUrl);
    
    if (embedUrlObj.searchParams.has('aws_auth_token')) {
      console.log('   ✅ Uses correct "aws_auth_token" parameter for Player SDK');
    } else if (embedUrlObj.searchParams.has('token')) {
      console.log('   ⚠️  Uses "token" parameter (should be "aws_auth_token" for Player SDK)');
    } else {
      console.log('   ❌ No auth token parameter found');
    }

    if (embedUrlObj.searchParams.has('channel')) {
      console.log('   ✅ Includes channel ARN parameter');
    } else {
      console.log('   ❌ Missing channel ARN parameter');
    }

    console.log('\n🎯 Test Results Summary:');
    console.log('========================');
    console.log('✅ JWT token generation: SUCCESS');
    console.log('✅ HLS URL generation: SUCCESS');
    console.log('✅ Player SDK URL generation: SUCCESS');
    console.log('\n📺 Frontend Integration:');
    console.log('   - Use authorizedStreamUrl for iframe playback');
    console.log('   - Use authorizedEmbedUrl for Player SDK integration');
    console.log('\n🚀 The authorizedStreamUrl is ready for testing!');

  } catch (error) {
    console.error('\n❌ Test Failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Stack: ${error.stack}`);
  }
}

testAuthorizedStreamUrl();

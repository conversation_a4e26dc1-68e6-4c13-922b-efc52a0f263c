const axios = require('axios');

async function debugChannelAccess() {
  console.log('🔍 Debugging AWS IVS Channel Access');
  console.log('===================================\n');

  const channelArn = 'arn:aws:ivs:eu-west-1:************:channel/y6EL0pKPjFNj';
  const channelId = 'y6EL0pKPjFNj';
  const baseUrl = 'https://71ce307069f0.eu-west-1.playback.live-video.net/api/video/v1/eu-west-1.************.channel.y6EL0pKPjFNj.m3u8';

  console.log('📋 Channel Information:');
  console.log(`   Channel ARN: ${channelArn}`);
  console.log(`   Channel ID: ${channelId}`);
  console.log(`   Base URL: ${baseUrl}\n`);

  // Test 1: Public access (no token)
  console.log('🌐 Test 1: Public Access (no token)');
  try {
    const response = await axios.get(baseUrl, { timeout: 10000 });
    console.log('✅ Public access successful');
    console.log(`   Response: ${response.data.substring(0, 100)}...`);
  } catch (error) {
    if (error.response) {
      console.log(`❌ Public access failed: ${error.response.status}`);
      console.log(`   Error: ${JSON.stringify(error.response.data)}`);
      
      if (error.response.status === 404 && error.response.data?.[0]?.error === 'Can not find channel') {
        console.log('   🔍 This suggests the channel is private or has access restrictions');
      }
    } else {
      console.log(`❌ Network error: ${error.message}`);
    }
  }

  // Test 2: With our generated token
  console.log('\n🔑 Test 2: With Generated Token');
  const tokenUrl = `${baseUrl}?token=eyJhbGciOiJFUzM4NCIsInR5cCI6IkpXVCIsImtpZCI6IjFpdHN2OHd6a2l2WiJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************.ITmfV0lTF_kiPVT2mqySXU4902SHJZ26nHV3JZcVRaj7f7y1Suf9L5pl6zOSob_dKYgc8eedUalMULWPW7YU4Bqr6PT8hXdcjaAvXxNYv1Rw-DWIu098tYGg7LItqDW4`;
  
  try {
    const response = await axios.get(tokenUrl, { timeout: 10000 });
    console.log('✅ Token access successful!');
    console.log(`   Response: ${response.data.substring(0, 100)}...`);
    console.log('🎉 SUCCESS: The channel is working with proper authorization!');
  } catch (error) {
    if (error.response) {
      console.log(`❌ Token access failed: ${error.response.status}`);
      console.log(`   Error: ${JSON.stringify(error.response.data)}`);
      
      if (error.response.status === 404) {
        console.log('\n🔍 Possible Issues:');
        console.log('   1. Channel might not be configured for private playback');
        console.log('   2. Key pair might not be associated with this channel');
        console.log('   3. Channel might be in wrong region or account');
        console.log('   4. Playback restriction policy might be misconfigured');
      } else if (error.response.status === 403) {
        console.log('\n🔍 Token Issues:');
        console.log('   1. Token might be expired');
        console.log('   2. Wrong key pair used for signing');
        console.log('   3. Token format might be incorrect');
      }
    } else {
      console.log(`❌ Network error: ${error.message}`);
    }
  }

  // Test 3: Alternative URL formats
  console.log('\n🔗 Test 3: Alternative URL Formats');
  
  const alternativeUrls = [
    // Try without the full path
    `https://71ce307069f0.eu-west-1.playback.live-video.net/api/video/v1/${channelId}.m3u8`,
    // Try with different account format
    `https://71ce307069f0.eu-west-1.playback.live-video.net/api/video/v1/channel.${channelId}.m3u8`,
    // Try the original format but different structure
    `https://71ce307069f0.eu-west-1.playback.live-video.net/api/video/v1/aws.ivs.eu-west-1.************.channel.${channelId}.m3u8`
  ];

  for (let i = 0; i < alternativeUrls.length; i++) {
    const altUrl = alternativeUrls[i];
    console.log(`\n   Testing URL ${i + 1}: ${altUrl}`);
    
    try {
      const response = await axios.get(altUrl, { timeout: 5000 });
      console.log(`   ✅ Alternative URL ${i + 1} works!`);
      console.log(`   Response: ${response.data.substring(0, 50)}...`);
    } catch (error) {
      if (error.response) {
        console.log(`   ❌ Alternative URL ${i + 1} failed: ${error.response.status}`);
        if (error.response.data) {
          console.log(`   Error: ${JSON.stringify(error.response.data)}`);
        }
      } else {
        console.log(`   ❌ Network error: ${error.message}`);
      }
    }
  }

  console.log('\n🎯 Summary and Recommendations:');
  console.log('================================');
  console.log('1. Check AWS IVS Console:');
  console.log('   - Verify channel is configured as "Private"');
  console.log('   - Check playback restriction policy');
  console.log('   - Ensure key pair "1itsv8wzkivZ" is associated');
  console.log('');
  console.log('2. Verify Channel State:');
  console.log('   - Channel should be "Active"');
  console.log('   - Check if there are any restrictions');
  console.log('   - Verify the channel is in eu-west-1 region');
  console.log('');
  console.log('3. Test with AWS CLI:');
  console.log('   aws ivs get-channel --arn "arn:aws:ivs:eu-west-1:************:channel/y6EL0pKPjFNj"');
  console.log('');
  console.log('4. Check Playback Restriction Policy:');
  console.log('   aws ivs get-playback-restriction-policy --arn <policy-arn>');
}

debugChannelAccess();

const { IvsClient, CreatePlaybackRestrictionPolicyCommand, UpdateChannelCommand, GetChannelCommand, GetPlaybackRestrictionPolicyCommand } = require('@aws-sdk/client-ivs');

// Load environment variables
require('dotenv').config();

async function setupChannelPrivateAccess() {
  console.log('🔧 Setting up Channel Private Access');
  console.log('===================================\n');

  const channelArn = 'arn:aws:ivs:eu-west-1:223358806502:channel/y6EL0pKPjFNj';
  const region = 'eu-west-1';

  // Initialize AWS IVS client
  const ivsClient = new IvsClient({
    region,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    }
  });

  try {
    console.log('📋 Channel Information:');
    console.log(`   Channel ARN: ${channelArn}`);
    console.log(`   Region: ${region}\n`);

    // Step 1: Check current channel configuration
    console.log('🔍 Step 1: Checking current channel configuration...');
    const getChannelCommand = new GetChannelCommand({ arn: channelArn });
    const channelResponse = await ivsClient.send(getChannelCommand);

    console.log('✅ Channel found:');
    console.log(`   Name: ${channelResponse.channel.name}`);
    console.log(`   Type: ${channelResponse.channel.type}`);
    console.log(`   Authorized: ${channelResponse.channel.authorized}`);
    console.log(`   Current Policy ARN: ${channelResponse.channel.playbackRestrictionPolicyArn || 'None'}\n`);

    // Step 2: Create playback restriction policy if needed
    let policyArn = channelResponse.channel.playbackRestrictionPolicyArn;

    if (!policyArn) {
      console.log('🔧 Step 2: Creating playback restriction policy...');

      const createPolicyCommand = new CreatePlaybackRestrictionPolicyCommand({
        name: 'video-stream-private-policy',
        enableStrictOriginEnforcement: true,
        allowedCountries: ['US', 'GB', 'ZW', 'ZA'], // Add your target countries
        allowedOrigins: [
          'https://localhost:3443',
          'https://your-production-domain.com', // Replace with your actual domain
          '*' // Allow all origins for testing (remove in production)
        ],
        tags: {
          'Environment': 'development',
          'Application': 'video-stream'
        }
      });

      try {
        const policyResponse = await ivsClient.send(createPolicyCommand);
        policyArn = policyResponse.playbackRestrictionPolicy.arn;
        console.log('✅ Playback restriction policy created:');
        console.log(`   Policy ARN: ${policyArn}\n`);
      } catch (error) {
        if (error.name === 'ConflictException') {
          console.log('⚠️  Policy already exists, trying to find existing policy...');
          // You might need to list policies and find the existing one
          console.log('   Please check AWS console for existing policies\n');
        } else {
          throw error;
        }
      }
    } else {
      console.log('✅ Channel already has a playback restriction policy\n');
    }

    // Step 3: Update channel with playback restriction policy
    if (policyArn && policyArn !== channelResponse.channel.playbackRestrictionPolicyArn) {
      console.log('🔧 Step 3: Associating policy with channel...');

      const updateChannelCommand = new UpdateChannelCommand({
        arn: channelArn,
        playbackRestrictionPolicyArn: policyArn
      });

      await ivsClient.send(updateChannelCommand);
      console.log('✅ Channel updated with playback restriction policy\n');
    }

    // Step 4: Verify the configuration
    console.log('🔍 Step 4: Verifying final configuration...');
    const finalChannelResponse = await ivsClient.send(getChannelCommand);

    console.log('✅ Final Channel Configuration:');
    console.log(`   Name: ${finalChannelResponse.channel.name}`);
    console.log(`   Authorized: ${finalChannelResponse.channel.authorized}`);
    console.log(`   Policy ARN: ${finalChannelResponse.channel.playbackRestrictionPolicyArn || 'None'}`);

    if (finalChannelResponse.channel.playbackRestrictionPolicyArn) {
      // Check policy details
      const getPolicyCommand = new GetPlaybackRestrictionPolicyCommand({
        arn: finalChannelResponse.channel.playbackRestrictionPolicyArn
      });

      const policyDetails = await ivsClient.send(getPolicyCommand);
      console.log('\n📋 Policy Details:');
      console.log(`   Name: ${policyDetails.playbackRestrictionPolicy.name}`);
      console.log(`   Allowed Countries: ${policyDetails.playbackRestrictionPolicy.allowedCountries?.join(', ') || 'All'}`);
      console.log(`   Allowed Origins: ${policyDetails.playbackRestrictionPolicy.allowedOrigins?.join(', ') || 'All'}`);
      console.log(`   Strict Origin Enforcement: ${policyDetails.playbackRestrictionPolicy.enableStrictOriginEnforcement}`);
    }

    console.log('\n🎉 SUCCESS! Channel is now configured for private access');
    console.log('\n🚀 Next Steps:');
    console.log('   1. Test the playback token generation again');
    console.log('   2. The "Can not find channel" error should be resolved');
    console.log('   3. Your authorizedStreamUrl should work correctly');

  } catch (error) {
    console.error('\n❌ Setup failed:');
    console.error(`   Error: ${error.message}`);

    if (error.name === 'AccessDeniedException') {
      console.error('\n💡 Solution: Check AWS credentials and permissions');
      console.error('   Make sure your AWS credentials have IVS permissions');
    } else if (error.name === 'ResourceNotFoundException') {
      console.error('\n💡 Solution: Verify the channel ARN is correct');
      console.error('   Check if the channel exists in the specified region');
    }
  }
}

// Run the setup
setupChannelPrivateAccess();

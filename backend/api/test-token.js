const jwt = require('jsonwebtoken');
const { IvsClient, ListPlaybackKeyPairsCommand } = require('@aws-sdk/client-ivs');

// Load environment variables
require('dotenv').config();

async function testTokenGeneration() {
  console.log('🔍 Testing AWS IVS Token Generation');
  console.log('=====================================\n');

  // Check environment variables
  const privateKey = process.env.IVS_SHARED_PLAYBACK_PRIVATE_KEY?.replace(/\\n/g, '\n');
  const keyName = process.env.IVS_SHARED_PLAYBACK_KEY_NAME || 'shared-playback-keys';
  
  console.log('✅ Environment Check:');
  console.log(`   Private key: ${privateKey ? 'Found (' + privateKey.length + ' chars)' : 'NOT FOUND'}`);
  console.log(`   Key name: ${keyName}`);
  console.log(`   AWS Region: ${process.env.AWS_REGION}`);
  console.log(`   AWS Access Key: ${process.env.AWS_ACCESS_KEY_ID ? process.env.AWS_ACCESS_KEY_ID.substring(0, 10) + '...' : 'NOT FOUND'}\n`);

  if (!privateKey) {
    console.error('❌ Private key not found in environment variables');
    return;
  }

  // Initialize AWS IVS client
  const ivsClient = new IvsClient({
    region: process.env.AWS_REGION,
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    },
  });

  try {
    // List available key pairs
    console.log('🔑 Available AWS IVS Key Pairs:');
    const command = new ListPlaybackKeyPairsCommand({});
    const response = await ivsClient.send(command);
    
    if (response.keyPairs && response.keyPairs.length > 0) {
      response.keyPairs.forEach((kp, index) => {
        const keyPairId = kp.arn?.split('/').pop() || 'Unknown';
        console.log(`   ${index + 1}. Name: ${kp.name || 'Unnamed'}`);
        console.log(`      Key Pair ID: ${keyPairId}`);
        console.log(`      ARN: ${kp.arn}`);
        console.log('');
      });
    } else {
      console.log('   No key pairs found\n');
    }

    // Find the correct key pair ID
    const targetKeyPair = response.keyPairs?.find(kp => kp.name === keyName);
    const keyPairId = targetKeyPair ? targetKeyPair.arn?.split('/').pop() : null;

    if (!keyPairId) {
      console.error(`❌ Key pair with name "${keyName}" not found in AWS IVS`);
      return;
    }

    console.log(`✅ Using key pair: ${keyName} (${keyPairId})\n`);

    // Test token generation
    console.log('🎫 Testing Token Generation:');
    
    const payload = {
      'aws:channel-arn': 'arn:aws:ivs:eu-west-1:223358806502:channel/y6EL0pKPjFNj',
      'exp': Math.floor(Date.now() / 1000) + 600, // 10 minutes from now
      'iat': Math.floor(Date.now() / 1000),
      'aws:viewer-id': '6fdae3b0-c18c-4c58-bbd0-ac8cbd49be37',
      'aws:viewer-session-version': Math.floor(Date.now() / 1000),
      'aws:access-control-allow-origin': '*'
    };

    const token = jwt.sign(payload, privateKey, {
      algorithm: 'ES384',
      header: {
        alg: 'ES384',
        typ: 'JWT',
        kid: keyPairId
      }
    });

    console.log('✅ Token generated successfully!');
    console.log(`   Token length: ${token.length} characters`);
    
    // Decode and verify token structure
    const parts = token.split('.');
    const header = JSON.parse(Buffer.from(parts[0], 'base64url').toString());
    const decodedPayload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    console.log('\n📋 Token Details:');
    console.log('   Header:', JSON.stringify(header, null, 4));
    console.log('   Payload:', JSON.stringify(decodedPayload, null, 4));
    console.log(`\n🔗 Test URL:`);
    console.log(`   https://player.live-video.net/1.23.0/amazon-ivs-player.html?channel=${encodeURIComponent(payload['aws:channel-arn'])}&aws_auth_token=${token}`);
    
    console.log('\n✅ Token generation test completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   1. Restart your backend server');
    console.log('   2. Try accessing the stream again');
    console.log('   3. Check that the channel uses the correct key pair ID');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testTokenGeneration();

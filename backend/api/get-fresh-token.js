const axios = require('axios');
const https = require('https');

// Create an agent that ignores SSL certificate errors for localhost
const httpsAgent = new https.Agent({
  rejectUnauthorized: false
});

async function getFreshToken() {
  console.log('🔑 Getting Fresh Keycloak Token');
  console.log('===============================\n');

  const keycloakUrl = 'https://localhost:9443/keycloak/realms/video-stream/protocol/openid-connect/token';
  
  try {
    console.log('🔄 Attempting to get fresh token from Keycloak...');
    
    const response = await axios.post(keycloakUrl, 
      'client_id=video-stream-api&grant_type=refresh_token&refresh_token=eyJhbGciOiJIUzUxMiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICIyNzhjZjRmMy1jYTlhLTRiNTQtYjAyMC05YzU2MmQ0NjBjMTMifQ.eyJleHAiOjE3NDg1NTk4NTcsImlhdCI6MTc0ODU1ODA1NywianRpIjoiNmMzNWZiODktMzJjMS00ZTk2LWEzNzAtMTM2N2UyMjJlMTYwIiwiaXNzIjoiaHR0cHM6Ly9sb2NhbGhvc3Q6OTQ0My9rZXljbG9hay9yZWFsbXMvdmlkZW8tc3RyZWFtIiwiYXVkIjoiaHR0cHM6Ly9sb2NhbGhvc3Q6OTQ0My9rZXljbG9hay9yZWFsbXMvdmlkZW8tc3RyZWFtIiwic3ViIjoiY2ZhNDRmMGMtY2NkMi00MzFlLWJjN2MtZDZjNmFmNjVjZTJlIiwidHlwIjoiUmVmcmVzaCIsImF6cCI6InZpZGVvLXN0cmVhbS1hcGkiLCJzaWQiOiI5YzA4OTgxMC03MWJiLTQzNGEtODIyYS01ODlmMjgwZmQ2MzYiLCJzY29wZSI6InByb2ZpbGUgYmFzaWMgZW1haWwgYWNyIHJvbGVzIHdlYi1vcmlnaW5zIn0.qcOUSYV_6Qr3bQ6F6lmeKVVf5nJwUc8qYfCZvCbbx_jsYWY3aztffv7FyL1M_pgW65jtqH3tQuELTDQGRezfNA&client_secret=UPiwrkAAiGOeCjhiFzfqNGmz9GXQz6AB',
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*'
        },
        httpsAgent,
        timeout: 10000
      }
    );

    const tokenData = response.data;
    console.log('✅ Fresh token obtained successfully!\n');
    
    console.log('📋 Token Information:');
    console.log(`   Access Token Length: ${tokenData.access_token?.length || 'N/A'} characters`);
    console.log(`   Token Type: ${tokenData.token_type || 'N/A'}`);
    console.log(`   Expires In: ${tokenData.expires_in || 'N/A'} seconds`);
    console.log(`   Refresh Token Length: ${tokenData.refresh_token?.length || 'N/A'} characters\n`);

    // Test the fresh token with the playback endpoint
    console.log('🎬 Testing fresh token with playback endpoint...');
    
    try {
      const playbackResponse = await axios.post('http://localhost:8000/api/events/event-1748546908092/playback-token', {
        durationMinutes: 10
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokenData.access_token}`
        },
        timeout: 15000
      });

      console.log('🎉 SUCCESS! Playback token generated with fresh token!\n');
      
      const result = playbackResponse.data;
      console.log('📊 Playback Token Response:');
      console.log(`   Requires Authorization: ${result.requiresAuthorization}`);
      console.log(`   Token Length: ${result.token?.length || 'N/A'} characters`);
      console.log(`   Expires In: ${result.expiresInMinutes} minutes`);
      console.log(`   Expires At: ${result.expiresAt}\n`);

      console.log('🔗 Generated URLs:');
      if (result.authorizedStreamUrl) {
        console.log(`   ✅ Authorized Stream URL: ${result.authorizedStreamUrl.substring(0, 100)}...`);
        
        // Check URL format
        if (result.authorizedStreamUrl.includes('?token=')) {
          console.log('   ✅ Uses correct "token" parameter for HLS');
        } else {
          console.log('   ⚠️  URL format might be incorrect');
        }
      }
      
      if (result.authorizedEmbedUrl) {
        console.log(`   ✅ Authorized Embed URL: ${result.authorizedEmbedUrl.substring(0, 100)}...`);
      }

      console.log('\n🎯 Next Steps:');
      console.log('   1. Use this fresh access token in your frontend');
      console.log('   2. The authorizedStreamUrl should work in your iframe');
      console.log('   3. The 403 authorization error should be resolved');
      
      console.log('\n📋 Fresh Access Token:');
      console.log(`Bearer ${tokenData.access_token}`);

    } catch (playbackError) {
      console.log('❌ Playback token test failed:');
      if (playbackError.response) {
        console.log(`   Status: ${playbackError.response.status}`);
        console.log(`   Error: ${playbackError.response.data?.message || 'Unknown error'}`);
      } else {
        console.log(`   Network error: ${playbackError.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Failed to get fresh token:');
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Error: ${error.response.data?.error_description || error.response.data?.error || 'Unknown error'}`);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   Keycloak server is not responding');
      console.error('   Make sure Keycloak is running on https://localhost:9443');
    } else {
      console.error(`   Network error: ${error.message}`);
    }
    
    console.log('\n💡 Alternative Solutions:');
    console.log('   1. Check if Keycloak is running');
    console.log('   2. Try logging in through the frontend to get a fresh token');
    console.log('   3. Use the browser\'s developer tools to copy a fresh token');
  }
}

getFreshToken();

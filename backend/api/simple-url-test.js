// Simple test to verify URL generation logic
console.log('🔗 Testing URL Generation Logic');
console.log('===============================\n');

// Test the URL generation logic directly
function testUrlGeneration() {
  const testToken = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
  
  // Test HLS URL (should get 'token' parameter)
  const hlsUrl = 'https://b37c565f6d790a14a0e78afaa6808a80.eu-west-1.playback.live-video.net/api/video/v1/aws.ivs.eu-west-1.223358806502.channel.y6EL0pKPjFNj.m3u8';
  
  console.log('🎥 Testing HLS URL:');
  console.log(`   Original: ${hlsUrl}`);
  
  // Apply the logic from our backend
  const hlsUrlObj = new URL(hlsUrl);
  if (hlsUrl.includes('.m3u8')) {
    hlsUrlObj.searchParams.set('token', testToken);
    console.log('   ✅ Applied "token" parameter (correct for HLS)');
  } else {
    hlsUrlObj.searchParams.set('aws_auth_token', testToken);
    console.log('   ⚠️  Applied "aws_auth_token" parameter');
  }
  
  const authorizedHlsUrl = hlsUrlObj.toString();
  console.log(`   Result: ${authorizedHlsUrl}\n`);
  
  // Test Player SDK URL (should get 'aws_auth_token' parameter)
  const playerUrl = 'https://player.live-video.net/1.23.0/amazon-ivs-player.html';
  const channelArn = 'arn:aws:ivs:eu-west-1:223358806502:channel/y6EL0pKPjFNj';
  
  console.log('🎮 Testing Player SDK URL:');
  console.log(`   Original: ${playerUrl}`);
  
  const playerUrlObj = new URL(playerUrl);
  playerUrlObj.searchParams.set('channel', channelArn);
  
  if (playerUrl.includes('.m3u8')) {
    playerUrlObj.searchParams.set('token', testToken);
    console.log('   ⚠️  Applied "token" parameter');
  } else {
    playerUrlObj.searchParams.set('aws_auth_token', testToken);
    console.log('   ✅ Applied "aws_auth_token" parameter (correct for Player SDK)');
  }
  
  const authorizedPlayerUrl = playerUrlObj.toString();
  console.log(`   Result: ${authorizedPlayerUrl}\n`);
  
  // Verify the results
  console.log('🔍 Verification:');
  console.log(`   HLS URL has 'token' param: ${authorizedHlsUrl.includes('?token=')}`);
  console.log(`   Player URL has 'aws_auth_token' param: ${authorizedPlayerUrl.includes('aws_auth_token=')}`);
  console.log(`   Player URL has 'channel' param: ${authorizedPlayerUrl.includes('channel=')}`);
  
  console.log('\n🎯 Summary:');
  console.log('   ✅ HLS URLs get "token" parameter');
  console.log('   ✅ Player SDK URLs get "aws_auth_token" parameter');
  console.log('   ✅ Player SDK URLs include channel ARN');
  
  console.log('\n📺 Frontend Usage:');
  console.log('   - authorizedStreamUrl (HLS): Use in iframe src');
  console.log('   - authorizedEmbedUrl (Player SDK): Use for advanced player features');
  
  return {
    authorizedStreamUrl: authorizedHlsUrl,
    authorizedEmbedUrl: authorizedPlayerUrl
  };
}

// Run the test
const result = testUrlGeneration();

console.log('\n🚀 Test Complete!');
console.log('   The URL generation logic is working correctly.');
console.log('   Frontend should prioritize authorizedStreamUrl for iframe playback.');

"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CommentSection } from "@/components/comments/CommentSection";
import { ArrowLeft, Send, MessageCircle, ThumbsUp, ThumbsDown, Share2, CreditCard, Bell, Clock, Calendar, MapPin, Play, Square, CheckCircle } from "lucide-react";
import { AuthorizedVideoPlayer } from "@/components/video/AuthorizedVideoPlayer";
import ErrorBoundary from "@/components/ErrorBoundary";
import { fetchEvent, checkEventAccess, purchaseEvent, getStreamStatus } from "@/services/event-service";
import { toggleLike, checkUserLiked } from "@/services/like-service";
import { incrementViewCount, checkUserViewed } from "@/services/view-service";
import { subscribeToChannel, unsubscribeFromChannel, checkSubscription } from "@/services/subscription-service";
import { Event } from "@/interfaces/types";
import { isAuthenticated } from "@/lib/auth";
import { convertNeo4jIntegers } from "@/utils/neo4j-utils";
import { PaymentModal } from "@/components/payments/PaymentModal";
import { updateEventSEO } from "@/utils/seo";
import { formatDateWithTimezone, calculateDuration, formatTimezone, getBrowserTimezone, getEventStatusMessage, isEventEnded, isEventLive } from "@/utils/date-utils";

interface WatchEventPageProps {
  id: string;
}

export default function WatchEventPage({ id }: WatchEventPageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isLiked, setIsLiked] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);
  const [activeTab, setActiveTab] = useState<"comments" | "live">("comments");
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscriberCount, setSubscriberCount] = useState(0);
  const [streamStatus, setStreamStatus] = useState<any>(null);
  const [isStreamLive, setIsStreamLive] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Load event data
  useEffect(() => {
    const loadEventData = async () => {
      try {
        setLoading(true);

        // Fetch event details
        const eventData = await fetchEvent(id);
        const safeEventData = convertNeo4jIntegers(eventData);

        // Log the event data for debugging
        console.log('Event data:', safeEventData);
        console.log('Channel data:', safeEventData.channel);

        setEvent(safeEventData);

        // Check if we've already viewed this event in this session
        let shouldIncrementView = true;

        try {
          // First check localStorage
          const storedWatchedEvents = localStorage.getItem('watchedEvents');
          const parsedWatchedEvents = storedWatchedEvents ? JSON.parse(storedWatchedEvents) : [];

          if (parsedWatchedEvents.includes(id)) {
            console.log(`Event ${id} already viewed in this session, skipping view increment`);
            shouldIncrementView = false;
          } else {
            // If not in localStorage, check with the backend (for authenticated users)
            if (isAuthenticated()) {
              const viewStatus = await checkUserViewed(id);
              if (viewStatus.viewed) {
                console.log(`User has already viewed event ${id}, skipping view increment`);
                shouldIncrementView = false;
              }
            }
          }

          // Only increment view if needed
          if (shouldIncrementView) {
            console.log(`Incrementing view count for event ${id}`);
            await incrementViewCount(id);

            // Add to localStorage to prevent future increments
            if (!parsedWatchedEvents.includes(id)) {
              parsedWatchedEvents.push(id);
              localStorage.setItem('watchedEvents', JSON.stringify(parsedWatchedEvents));
            }
          }
        } catch (error) {
          console.error('Failed to handle view count:', error);
        }

        // Check if user has access to this event
        if (isAuthenticated()) {
          try {
            const accessData = await checkEventAccess(id);
            setHasAccess(accessData);
          } catch (error) {
            console.error('Failed to check event access:', error);
            setHasAccess(false);
          }

          // Check like status
          try {
            const likeStatus = await checkUserLiked(id);
            setIsLiked(likeStatus.liked);
          } catch (error) {
            console.error('Failed to check like status:', error);
            setIsLiked(false);
          }

          // Check subscription status if the event has a channel
          if (safeEventData.channel && safeEventData.channel.id) {
            try {
              const subscriptionStatus = await checkSubscription(safeEventData.channel.id);
              setIsSubscribed(subscriptionStatus.subscribed);
              setSubscriberCount(subscriptionStatus.subscriberCount);
            } catch (error) {
              console.error('Failed to check subscription status:', error);
              setIsSubscribed(false);
            }
          }
        } else if (safeEventData.channel && safeEventData.channel.id) {
          // For non-authenticated users, just get the subscriber count
          try {
            const countData = await checkSubscription(safeEventData.channel.id);
            setSubscriberCount(countData.subscriberCount);
          } catch (error) {
            console.error('Failed to get subscriber count:', error);
          }
        }
      } catch (err) {
        console.error('Failed to load event data', err);
        setError('Failed to load event data');
        toast({
          title: "Error",
          description: "Failed to load event data",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    loadEventData();
  }, [id, toast]);

  // Update SEO when event data is loaded
  useEffect(() => {
    if (event) {
      const eventUrl = typeof window !== 'undefined' ? window.location.href : '';

      updateEventSEO({
        title: `${event.title} - Watch Live Event`,
        description: event.description || `Watch ${event.title} live on our platform`,
        image: event.imageUrl,
        startTime: event.startTime,
        endTime: event.endTime,
        price: event.price,
        currency: event.currency,
        channelName: event.channel?.name,
        eventUrl
      });
    }
  }, [event]);

  // Function to check stream status (only for current/upcoming events)
  const checkStreamStatus = async () => {
    if (!event?.id || !event?.startTime || !event?.endTime) return;

    // Don't check stream status for past events since we use one playback URL per channel
    const eventIsEnded = isEventEnded(event.startTime, event.endTime);
    if (eventIsEnded) {
      setIsStreamLive(false);
      return;
    }

    try {
      const status = await getStreamStatus(event.id);
      setStreamStatus(status);
      setIsStreamLive(status.streamLive || false);

      // Only update stream URL if user has access and URL is available
      if (status.found && status.hasAccess && (status.event.streamUrl || status.event.embedUrl || status.event.authorizedStreamUrl || status.event.authorizedEmbedUrl)) {
        setEvent(prevEvent => prevEvent ? {
          ...prevEvent,
          streamUrl: status.event.streamUrl,
          embedUrl: status.event.embedUrl,
          authorizedStreamUrl: status.event.authorizedStreamUrl,
          authorizedEmbedUrl: status.event.authorizedEmbedUrl,
          requiresAuthorization: status.requiresAuthorization
        } : null);
      }
    } catch (error) {
      console.error('Failed to check stream status:', error);
    }
  };

  // Real-time status updates - refresh every minute to update relative times and check stream status
  useEffect(() => {
    if (!event || !event.startTime || !event.endTime) return;

    // Initial stream status check
    checkStreamStatus();

    const interval = setInterval(() => {
      // Force a re-render to update relative times like "ends in 5 minutes"
      // This is a simple way to keep the status messages current
      setEvent(prevEvent => prevEvent ? { ...prevEvent } : null);

      // Check stream status every minute for current/upcoming events only
      // Don't check for past events since we use one playback URL per channel
      const eventEnded = event.startTime && event.endTime && isEventEnded(event.startTime, event.endTime);
      if (!eventEnded && (event.status === 'live' || event.status === 'published')) {
        checkStreamStatus();
      }
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [event?.startTime, event?.endTime, event?.id, event?.status]);

  const handlePaymentSuccess = () => {
    // Close the payment modal
    setIsPaymentModalOpen(false);

    // Update the hasAccess state
    setHasAccess(true);

    // Trigger refresh in AuthorizedVideoPlayer
    setRefreshTrigger(prev => prev + 1);

    toast({
      title: "Payment Successful",
      description: "You now have access to this event. Enjoy!",
    });
  };

  const handlePurchase = () => {
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to purchase this event",
        variant: "default"
      });
      router.push('/login');
      return;
    }

    // Open the payment modal
    setIsPaymentModalOpen(true);
  };

  const handleLike = async () => {
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to like this event",
        variant: "default"
      });
      return;
    }

    try {
      // Optimistic update
      setIsLiked(!isLiked);

      const result = await toggleLike(id);

      // Update state based on actual result
      setIsLiked(result.liked);

      toast({
        title: result.liked ? "Liked" : "Unliked",
        description: result.liked ? "Added to your liked events" : "Removed from your liked events",
      });
    } catch (error) {
      console.error('Failed to toggle like:', error);
      // Revert optimistic update
      setIsLiked(!isLiked);
      toast({
        title: "Error",
        description: "Failed to update like status. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSubscribe = async () => {
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to subscribe to this channel",
        variant: "default"
      });
      return;
    }

    if (!event?.channel?.id) {
      toast({
        title: "Error",
        description: "Channel information not available",
        variant: "destructive"
      });
      return;
    }

    try {
      // Optimistic update
      setIsSubscribed(!isSubscribed);
      setSubscriberCount(prev => isSubscribed ? prev - 1 : prev + 1);

      const result = isSubscribed
        ? await unsubscribeFromChannel(event.channel.id)
        : await subscribeToChannel(event.channel.id);

      // Update state based on actual result
      setIsSubscribed(result.subscribed);
      setSubscriberCount(result.subscriberCount);

      toast({
        title: result.subscribed ? "Subscribed" : "Unsubscribed",
        description: result.subscribed
          ? `You are now subscribed to ${event.channel.name}`
          : `You have unsubscribed from ${event.channel.name}`,
      });
    } catch (error) {
      console.error('Failed to update subscription:', error);
      // Revert optimistic update
      setIsSubscribed(!isSubscribed);
      setSubscriberCount(prev => isSubscribed ? prev + 1 : prev - 1);
      toast({
        title: "Error",
        description: "Failed to update subscription. Please try again.",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="container py-8">
        <div className="flex justify-center items-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="container py-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Event not found</h2>
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={() => router.push('/home')}>Back to Home</Button>
      </div>
    );
  }

  // Check if the event is live or published
  const canWatch = event.status === 'live' || event.status === 'published';

  // Check if user has purchased the event or if it's free
  const needsPurchase = event.price && event.price > 0 && !hasAccess;

  // Ensure all Neo4j integers are converted to regular numbers/strings
  const safeEvent = convertNeo4jIntegers(event);
  const eventIdString = String(safeEvent.id);

  // Use real-time stream status if available, otherwise fall back to event status
  const isLive = isStreamLive || safeEvent.status === 'live';
  // Only use stream URL if user has stream access (checked by backend for event timing)
  // Prioritize HLS stream URLs over embed URLs for direct playback
  const effectiveStreamUrl = (streamStatus?.allowStreamAccess !== false) ?
    (streamStatus?.event?.authorizedStreamUrl || streamStatus?.event?.streamUrl || streamStatus?.event?.embedUrl || safeEvent.streamUrl || safeEvent.embedUrl) : null;

  return (
    <div className="container max-w-[1800px] py-6">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push('/home')}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Home
      </Button>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* Video Player with Authorization */}
          <ErrorBoundary>
            <AuthorizedVideoPlayer
              eventId={eventIdString}
              className="mb-4"
              autoplay={false}
              eventThumbnail={safeEvent.imageUrl}
              eventTitle={safeEvent.title}
              eventPrice={safeEvent.price}
              eventCurrency={safeEvent.currency}
              refreshTrigger={refreshTrigger}
              onError={(error) => {
                console.error('Video Player error:', error);
                toast({
                  title: "Stream Error",
                  description: error,
                  variant: "destructive"
                });
              }}
              onTokenRefresh={(token) => {
                console.log('Playback token refreshed for event:', eventIdString);
              }}
              onPurchaseRequired={handlePurchase}
            />
          </ErrorBoundary>

          <h1 className="text-xl font-bold mb-2">{safeEvent.title}</h1>
          <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground mb-4">
            <span>{safeEvent.views || 0} views</span>
            <span>•</span>
            <span>{safeEvent.likeCount || 0} likes</span>
            <span className={`ml-2 inline-flex items-center px-2 py-1 text-xs rounded-full ${
              safeEvent.status === 'live' ? 'bg-red-100 text-red-800' :
              safeEvent.status === 'published' ? 'bg-blue-100 text-blue-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {safeEvent.status === 'live' ? 'LIVE' :
               safeEvent.status === 'published' ? 'PUBLISHED' :
               safeEvent.status}
            </span>
          </div>

          <Card className="mb-4">
            <div className="p-4 border-b">
              <div className="flex items-center gap-4 mb-4">
                <Button
                  variant={activeTab === "comments" ? "default" : "outline"}
                  onClick={() => setActiveTab("comments")}
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Comments
                </Button>
                {isLive && !isEventEnded(safeEvent.startTime, safeEvent.endTime) && (
                  <Button
                    variant={activeTab === "live" ? "default" : "outline"}
                    onClick={() => setActiveTab("live")}
                  >
                    <MessageCircle className="h-4 w-4 mr-2" fill="currentColor" />
                    Live Chat
                  </Button>
                )}
              </div>

              {activeTab === "comments" ? (
                <CommentSection eventId={eventIdString} />
              ) : (
                <>
                  <div className="flex gap-2">
                    <Textarea
                      placeholder={isAuthenticated() ? "Send a message..." : "Sign in to chat"}
                      disabled={!isAuthenticated()}
                      className="min-h-[60px]"
                    />
                    <Button
                      className="flex-shrink-0 h-full"
                      disabled={!isAuthenticated()}
                      variant="default"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>

                  <ScrollArea className="h-[400px]">
                    <div className="p-4 text-center text-muted-foreground">
                      {isAuthenticated() ? "No messages yet" : "Sign in to participate in live chat"}
                    </div>
                  </ScrollArea>
                </>
              )}
            </div>
          </Card>
        </div>

        <div className="space-y-4">
          {/* Channel Information Card */}
          {safeEvent.channel && (
            <Card className="p-4">
              <div className="flex items-center gap-3 mb-3">
                <div
                  className="h-12 w-12 rounded-full overflow-hidden bg-gray-200 cursor-pointer"
                  onClick={() => {
                    if (safeEvent.channel?.id) {
                      router.push(`/channel-view/${safeEvent.channel.id}`);
                    }
                  }}
                >
                  <img
                    src={safeEvent.channel.imageUrl || '/placeholder.svg'}
                    alt={safeEvent.channel.name}
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      // If image fails to load, replace with placeholder
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder.svg';
                    }}
                  />
                </div>
                <div
                  className="flex-1 cursor-pointer"
                  onClick={() => {
                    if (safeEvent.channel?.id) {
                      router.push(`/channel-view/${safeEvent.channel.id}`);
                    }
                  }}
                >
                  <h3 className="font-medium hover:text-primary">{safeEvent.channel.name}</h3>
                  <p className="text-sm text-muted-foreground">{subscriberCount} subscribers</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex items-center gap-2"
                    onClick={() => {
                      if (safeEvent.channel?.id) {
                        router.push(`/channel-view/${String(safeEvent.channel.id)}`);
                      }
                    }}
                  >
                    View
                  </Button>
                  <Button
                    variant={isSubscribed ? "default" : "outline"}
                    className="flex items-center gap-2"
                    onClick={handleSubscribe}
                  >
                    <Bell className={`h-4 w-4 ${isSubscribed ? "fill-white" : ""}`} />
                    {isSubscribed ? "Subscribed" : "Subscribe"}
                  </Button>
                </div>
              </div>
            </Card>
          )}

          {/* Event Details Card */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">Event Details</h3>
              {/* Event Status Badge */}
              {safeEvent.startTime && safeEvent.endTime && (() => {
                const statusInfo = getEventStatusMessage(safeEvent.startTime, safeEvent.endTime);
                const getStatusIcon = () => {
                  switch (statusInfo.status) {
                    case 'live':
                      return <Play className="h-3 w-3 mr-1" />;
                    case 'ended':
                      return <CheckCircle className="h-3 w-3 mr-1" />;
                    case 'upcoming':
                      return <Clock className="h-3 w-3 mr-1" />;
                    default:
                      return <Clock className="h-3 w-3 mr-1" />;
                  }
                };

                return (
                  <Badge variant={statusInfo.variant} className="flex items-center">
                    {getStatusIcon()}
                    {statusInfo.message}
                  </Badge>
                );
              })()}
            </div>

            <p className="text-sm whitespace-pre-wrap mb-4">{safeEvent.description}</p>

            {/* Event Schedule Information */}
            <div className="space-y-3 mb-4 p-3 bg-muted/50 rounded-lg">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Event Schedule
              </h4>

              {/* Start Time */}
              {safeEvent.startTime && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-3 w-3" />
                    <span className="font-medium">Start Time:</span>
                  </div>
                  {(() => {
                    const startTimeInfo = formatDateWithTimezone(safeEvent.startTime, safeEvent.timezone);
                    const browserTimezone = getBrowserTimezone();
                    const showBrowserTime = safeEvent.timezone && safeEvent.timezone !== browserTimezone;

                    return (
                      <div className="ml-5 space-y-1">
                        <div className="text-sm">
                          {startTimeInfo.eventTime}
                          {safeEvent.timezone && (
                            <span className="text-muted-foreground ml-1">
                              ({formatTimezone(safeEvent.timezone)})
                            </span>
                          )}
                        </div>
                        {showBrowserTime && (
                          <div className="text-xs text-muted-foreground">
                            Your time: {startTimeInfo.browserTime} ({formatTimezone(browserTimezone)})
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* End Time */}
              {safeEvent.endTime && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-3 w-3" />
                    <span className="font-medium">End Time:</span>
                  </div>
                  {(() => {
                    const endTimeInfo = formatDateWithTimezone(safeEvent.endTime, safeEvent.timezone);
                    const browserTimezone = getBrowserTimezone();
                    const showBrowserTime = safeEvent.timezone && safeEvent.timezone !== browserTimezone;

                    return (
                      <div className="ml-5 space-y-1">
                        <div className="text-sm">
                          {endTimeInfo.eventTime}
                          {safeEvent.timezone && (
                            <span className="text-muted-foreground ml-1">
                              ({formatTimezone(safeEvent.timezone)})
                            </span>
                          )}
                        </div>
                        {showBrowserTime && (
                          <div className="text-xs text-muted-foreground">
                            Your time: {endTimeInfo.browserTime} ({formatTimezone(browserTimezone)})
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* Duration */}
              {safeEvent.startTime && safeEvent.endTime && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-3 w-3" />
                    <span className="font-medium">Duration:</span>
                  </div>
                  <div className="ml-5 text-sm">
                    {calculateDuration(safeEvent.startTime, safeEvent.endTime)}
                  </div>
                </div>
              )}

              {/* Price Information */}
              {safeEvent.price && safeEvent.price > 0 && (
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm">
                    <CreditCard className="h-3 w-3" />
                    <span className="font-medium">Price:</span>
                  </div>
                  <div className="ml-5 text-sm">
                    {safeEvent.price} {safeEvent.currency || 'USD'}
                  </div>
                </div>
              )}

              {/* Stream Status Debug Info (for development) */}
              {process.env.NODE_ENV === 'development' && (
                <div className="space-y-1 p-2 bg-gray-100 rounded text-xs">
                  <div className="font-medium">Stream Debug Info:</div>
                  <div>Event Status: {safeEvent.status}</div>
                  {(() => {
                    const eventEnded = safeEvent.startTime && safeEvent.endTime && isEventEnded(safeEvent.startTime, safeEvent.endTime);
                    if (eventEnded) {
                      return <div className="text-orange-600">Past Event: Stream check skipped (using shared playback URL)</div>;
                    } else if (streamStatus) {
                      return (
                        <>
                          <div>Stream Live: {isStreamLive ? 'Yes' : 'No'}</div>
                          <div>User Has Access: {streamStatus.hasAccess ? 'Yes' : 'No'}</div>
                          <div>Stream Access Allowed: {streamStatus.allowStreamAccess ? 'Yes' : 'No'}</div>
                          <div>Event Currently Scheduled: {streamStatus.isEventCurrentlyScheduled ? 'Yes' : 'No'}</div>
                          <div>Requires Authorization: {streamStatus.requiresAuthorization ? 'Yes' : 'No'}</div>
                          <div>Channel Has Playback Keys: {streamStatus.channel?.hasPlaybackKeys ? 'Yes' : 'No'}</div>
                          <div>Stream URL: {effectiveStreamUrl ? 'Available' : 'None'}</div>
                          <div>Authorized URLs: {(streamStatus.event?.authorizedStreamUrl || streamStatus.event?.authorizedEmbedUrl) ? 'Available' : 'None'}</div>
                          <div>Channel ARN: {streamStatus.channel?.channelArn ? 'Available' : 'None'}</div>
                          {!streamStatus.allowStreamAccess && streamStatus.hasAccess && (
                            <div className="text-red-600">Access denied: Event not currently scheduled</div>
                          )}
                        </>
                      );
                    } else {
                      return <div>Stream Status: Checking...</div>;
                    }
                  })()}
                  {safeEvent.price && safeEvent.price > 0 && (
                    <div>Event Price: {safeEvent.price} {safeEvent.currency}</div>
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center gap-2 mt-4">
              <Button
                variant="outline"
                className="flex-1"
                onClick={handleLike}
                disabled={safeEvent.startTime && safeEvent.endTime && isEventEnded(safeEvent.startTime, safeEvent.endTime)}
              >
                <ThumbsUp className={`h-4 w-4 mr-2 ${isLiked ? "fill-primary" : ""}`} />
                {isLiked ? "Liked" : "Like"}
              </Button>
              <Button variant="outline" className="flex-1">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </Card>
        </div>
      </div>

      {/* Payment Modal */}
      {event && (
        <PaymentModal
          isOpen={isPaymentModalOpen}
          onClose={() => setIsPaymentModalOpen(false)}
          eventId={eventIdString}
          amount={event.price}
          currency={event.currency}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </div>
  );
}

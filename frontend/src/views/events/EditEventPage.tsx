"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useParams } from 'react-router-dom';
import { useAppContext } from "@/context/NextAppContext";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Heading } from "@/components/ui/heading";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import { ArrowLeft, Upload, Loader2, CheckCircle } from "lucide-react";
import { Event } from "@/interfaces/types";
import { fetchEvent, updateEvent, updateEventThumbnail, publishEvent } from "@/services/event-service";
import { fetchPricingPlans } from "@/services/pricing-plan-service";
import { fetchCategories } from "@/services/category-service";
import { COUNTRIES } from "@/lib/constants/pricing";
import { parseNeo4jDateTime } from "@/utils/date-utils";

// Categories and pricing plans will be fetched from the backend

// Event duration limits in hours
const MIN_EVENT_DURATION = 1;
const MAX_EVENT_DURATION = 4;

const TIMEZONES = [
  "America/New_York",
  "America/Los_Angeles",
  "America/Chicago",
  "America/Denver",
  "Europe/London",
  "Europe/Paris",
  "Asia/Tokyo",
  "Asia/Dubai",
  "Australia/Sydney",
  "Pacific/Auckland"
];

const editEventSchema = z.object({
  title: z.string().min(2, "Title must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  startTime: z.string().min(1, "Start time is required"),
  endTime: z.string().min(1, "End time is required"),
  duration: z.coerce.number()
    .min(MIN_EVENT_DURATION, `Event must be at least ${MIN_EVENT_DURATION} hour`)
    .max(MAX_EVENT_DURATION, `Event cannot exceed ${MAX_EVENT_DURATION} hours`),
  timezone: z.string().min(1, "Timezone is required"),
  forKids: z.boolean(),
  viewersOverEighteen: z.boolean(),
  videoAddress: z.object({
    countryCode: z.string().min(2, "Country is required"),
    streetName: z.string(),
    streetNumber: z.string(),
    province: z.string(),
    postalCode: z.string(),
  }),
  tags: z.array(z.string()),
  category: z.string().min(1, "Category is required"),
  pricingPlanId: z.string().min(1, "Pricing plan is required"),
  thumbnail: z.any()
})
.refine(
  (data) => {
    if (!data.startTime || !data.endTime) return true;

    const start = new Date(data.startTime);
    const end = new Date(data.endTime);

    // Check if dates are valid
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return true;

    // Calculate difference in milliseconds
    const diffMs = end.getTime() - start.getTime();

    // End time must be after start time and the difference must be at least MIN_EVENT_DURATION hours
    return diffMs > 0 && diffMs >= MIN_EVENT_DURATION * 60 * 60 * 1000;
  },
  {
    message: `End time must be after start time by at least ${MIN_EVENT_DURATION} hour(s)`,
    path: ["endTime"],
  }
);

export default function EditEventPage({ eventId: propEventId }: { eventId?: string } = {}) {
  const router = useRouter();
  const params = useParams();
  const { events, setEvents } = useAppContext();

  // Get the ID from props first, then fall back to URL parameters
  const id = propEventId || (params.id as string);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUploadingThumbnail, setIsUploadingThumbnail] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for pricing plans and categories
  const [pricingPlans, setPricingPlans] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [loadingPricingPlans, setLoadingPricingPlans] = useState(true);
  const [loadingCategories, setLoadingCategories] = useState(true);

  // Initialize form with default values
  const form = useForm({
    resolver: zodResolver(editEventSchema),
    defaultValues: {
      title: "",
      description: "",
      startTime: "",
      endTime: "",
      duration: MIN_EVENT_DURATION,
      timezone: "America/New_York",
      forKids: false,
      viewersOverEighteen: false,
      videoAddress: {
        countryCode: "US",
        streetName: "",
        streetNumber: "",
        province: "",
        postalCode: "",
      },
      tags: [],
      category: '',
      pricingPlanId: '',
      thumbnail: null
    },
  });

  // Fetch pricing plans from the API
  useEffect(() => {
    const loadPricingPlans = async () => {
      try {
        setLoadingPricingPlans(true);
        const data = await fetchPricingPlans();
        setPricingPlans(data);
      } catch (error) {
        console.error('Failed to load pricing plans:', error);
        toast.error('Failed to load pricing plans');
      } finally {
        setLoadingPricingPlans(false);
      }
    };

    loadPricingPlans();
  }, []);

  // Fetch categories from the API
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setLoadingCategories(true);
        const data = await fetchCategories();
        setCategories(data);
      } catch (error) {
        console.error('Failed to load categories:', error);
        toast.error('Failed to load categories');
      } finally {
        setLoadingCategories(false);
      }
    };

    loadCategories();
  }, []);

  // Fetch the event data from the API
  useEffect(() => {
    const loadEventData = async () => {
      try {
        setLoading(true);
        const eventData = await fetchEvent(id);
        setEvent(eventData);

        // Set thumbnail preview if available
        if (eventData.imageUrl) {
          setThumbnailPreview(eventData.imageUrl);
        }

        // Parse date/time fields from the API response
        const startDate = parseNeo4jDateTime(eventData.startTime);
        const endDate = parseNeo4jDateTime(eventData.endTime);

        // Convert UTC time to local time in the event's timezone for editing
        const convertFromUTCToEventTimezone = (utcDate: Date, timezone: string): string => {
          try {
            if (!utcDate || !timezone) return "";

            // Get the date/time components as they would appear in the event's timezone
            const formatter = new Intl.DateTimeFormat('en-CA', {
              timeZone: timezone,
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit',
              hour12: false
            });

            const parts = formatter.formatToParts(utcDate);
            const year = parts.find(p => p.type === 'year')?.value;
            const month = parts.find(p => p.type === 'month')?.value;
            const day = parts.find(p => p.type === 'day')?.value;
            const hour = parts.find(p => p.type === 'hour')?.value;
            const minute = parts.find(p => p.type === 'minute')?.value;

            // Return in the format expected by datetime-local input
            return `${year}-${month}-${day}T${hour}:${minute}`;
          } catch (error) {
            console.error('Error converting UTC to event timezone:', error);
            // Fallback to simple ISO format
            return utcDate.toISOString().slice(0, 16);
          }
        };

        // Format dates for datetime-local input (YYYY-MM-DDTHH:MM)
        const formatDateForInput = (date: Date | null, timezone: string): string => {
          if (!date) return "";
          // Convert UTC date to the event's timezone for display
          return convertFromUTCToEventTimezone(date, timezone);
        };

        // Get the event timezone (fallback to default if not available)
        const eventTimezone = eventData.timezone || "America/New_York";

        // Format start and end times in the event's timezone
        const formattedStartTime = formatDateForInput(startDate, eventTimezone);
        const formattedEndTime = formatDateForInput(endDate, eventTimezone);

        // Calculate duration in hours
        let duration = MIN_EVENT_DURATION;
        if (startDate && endDate) {
          const durationMs = endDate.getTime() - startDate.getTime();
          const durationHours = durationMs / (1000 * 60 * 60);
          duration = Math.max(MIN_EVENT_DURATION, Math.min(MAX_EVENT_DURATION, Math.round(durationHours * 10) / 10));
        }

        // Update form values with event data
        form.reset({
          title: eventData.title || "",
          description: eventData.description || "",
          startTime: formattedStartTime,
          endTime: formattedEndTime,
          duration: duration,
          timezone: eventTimezone,
          forKids: eventData.forKids || false,
          viewersOverEighteen: eventData.viewersOverEighteen || false,
          videoAddress: {
            countryCode: eventData.videoAddress?.countryIso2 || "US",
            streetName: eventData.videoAddress?.streetName || "",
            streetNumber: eventData.videoAddress?.streetNumber || "",
            province: eventData.videoAddress?.province || "",
            postalCode: eventData.videoAddress?.postalCode || "",
          },
          tags: eventData.tags || [],
          category: eventData.category || '',
          pricingPlanId: eventData.pricingPlanId || '',
          thumbnail: null
        });
      } catch (error) {
        console.error('Failed to load event data:', error);
        setError('Failed to load event data');
        toast.error('Failed to load event data');
      } finally {
        setLoading(false);
      }
    };

    loadEventData();
  }, [id, router, form]);

  // Calculate duration in hours between two datetime strings
  const calculateDuration = (startTime: string, endTime: string): number => {
    if (!startTime || !endTime) return MIN_EVENT_DURATION;

    try {
      // Parse the datetime strings
      const start = new Date(startTime);
      const end = new Date(endTime);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) return MIN_EVENT_DURATION;

      // If end time is before or equal to start time, return minimum duration
      if (end <= start) return MIN_EVENT_DURATION;

      // Calculate duration in milliseconds
      const durationMs = end.getTime() - start.getTime();

      // Convert to hours and round to 1 decimal place for better UX
      const durationHours = durationMs / (1000 * 60 * 60);
      const roundedDuration = Math.round(durationHours * 10) / 10;

      // Ensure duration is within allowed range
      return Math.max(MIN_EVENT_DURATION, Math.min(MAX_EVENT_DURATION, roundedDuration));
    } catch (error) {
      console.error('Error calculating duration:', error);
      return MIN_EVENT_DURATION;
    }
  };

  // Update end time based on start time and duration
  const updateEndTime = (startTime: string, durationHours: number): string => {
    if (!startTime) return "";

    try {
      // Parse the start time string
      const start = new Date(startTime);
      if (isNaN(start.getTime())) return "";

      // Create a new date object for the end time
      const end = new Date(start);

      // Add the duration in hours
      end.setHours(end.getHours() + durationHours);

      // Format as YYYY-MM-DDTHH:MM for datetime-local input
      const year = end.getFullYear();
      const month = String(end.getMonth() + 1).padStart(2, '0');
      const day = String(end.getDate()).padStart(2, '0');
      const hours = String(end.getHours()).padStart(2, '0');
      const minutes = String(end.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day}T${hours}:${minutes}`;
    } catch (error) {
      console.error('Error updating end time:', error);
      return "";
    }
  };

  // Handle start time change
  const handleStartTimeChange = (value: string) => {
    const startDate = new Date(value);
    if (isNaN(startDate.getTime())) return;

    const currentDuration = form.getValues("duration");
    const newEndTime = updateEndTime(value, currentDuration);

    // Get current end time
    const currentEndTime = form.getValues("endTime");
    const currentEndDate = new Date(currentEndTime);

    // Only update end time if:
    // 1. There is no current end time
    // 2. The new start time would make the current end time invalid
    // 3. The current end time is before the new start time
    if (!currentEndTime ||
        isNaN(currentEndDate.getTime()) ||
        currentEndDate <= startDate ||
        (currentEndDate.getTime() - startDate.getTime()) < MIN_EVENT_DURATION * 60 * 60 * 1000) {
      form.setValue("endTime", newEndTime);
    }

    form.setValue("startTime", value);

    // Recalculate duration based on the (potentially updated) start and end times
    const endTime = form.getValues("endTime");
    if (endTime) {
      const newDuration = calculateDuration(value, endTime);
      form.setValue("duration", newDuration);
    }
  };

  // Handle end time change
  const handleEndTimeChange = (value: string) => {
    try {
      const endDate = new Date(value);
      if (isNaN(endDate.getTime())) return;

      const startTime = form.getValues("startTime");
      if (!startTime) {
        // If there's no start time yet, just set the end time
        form.setValue("endTime", value);
        return;
      }

      const startDate = new Date(startTime);
      if (isNaN(startDate.getTime())) {
        // If start time is invalid, just set the end time
        form.setValue("endTime", value);
        return;
      }

      // Format dates for comparison (to avoid timezone issues)
      const formatDate = (date: Date): string => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day}T${hours}:${minutes}`;
      };

      // Calculate minimum valid end time (start time + MIN_EVENT_DURATION)
      const minEndDate = new Date(startDate);
      minEndDate.setHours(minEndDate.getHours() + MIN_EVENT_DURATION);
      const minEndTime = formatDate(minEndDate);

      // Check if end time is before or too close to start time
      if (endDate <= startDate || endDate < minEndDate) {
        // Adjust end time to be start time + MIN_EVENT_DURATION
        form.setValue("endTime", minEndTime);

        // Show a toast notification
        toast.warning(`End time must be at least ${MIN_EVENT_DURATION} hour(s) after start time. Adjusted automatically.`);

        // Update duration to minimum
        form.setValue("duration", MIN_EVENT_DURATION);
      } else {
        // End time is valid, set it and update duration
        form.setValue("endTime", value);

        // Calculate and update duration
        const newDuration = calculateDuration(startTime, value);
        form.setValue("duration", newDuration);
      }
    } catch (error) {
      console.error('Error handling end time change:', error);
      // In case of error, just set the value without validation
      form.setValue("endTime", value);
    }
  };

  // Handle duration change
  const handleDurationChange = (value: number) => {
    // Ensure duration is within allowed range
    let validatedValue = value;

    if (isNaN(validatedValue) || validatedValue < MIN_EVENT_DURATION) {
      validatedValue = MIN_EVENT_DURATION;
      toast.warning(`Event duration must be at least ${MIN_EVENT_DURATION} hour(s). Adjusted automatically.`);
    } else if (validatedValue > MAX_EVENT_DURATION) {
      validatedValue = MAX_EVENT_DURATION;
      toast.warning(`Event duration cannot exceed ${MAX_EVENT_DURATION} hours. Adjusted automatically.`);
    }

    form.setValue("duration", validatedValue);

    const startTime = form.getValues("startTime");
    if (startTime) {
      const startDate = new Date(startTime);
      if (!isNaN(startDate.getTime())) {
        const newEndTime = updateEndTime(startTime, validatedValue);
        form.setValue("endTime", newEndTime);
      }
    }
  };

  const handleThumbnailChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Create a preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setThumbnailPreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Upload the thumbnail
    try {
      setIsUploadingThumbnail(true);
      const updatedEvent = await updateEventThumbnail(id, file);

      // Update the event with the new thumbnail URL
      setEvent(updatedEvent);

      toast.success("Thumbnail updated successfully");
    } catch (error) {
      console.error('Failed to update thumbnail:', error);
      toast.error("Failed to update thumbnail");
    } finally {
      setIsUploadingThumbnail(false);
    }
  };

  const handlePublishEvent = async () => {
    if (!event) return;

    // Check if all required fields are filled
    const missingFields = [];

    if (!event.title || event.title.trim() === '') {
      missingFields.push('Title');
    }

    if (!event.description || event.description.trim() === '') {
      missingFields.push('Description');
    }

    if (!event.imageUrl || event.imageUrl.trim() === '') {
      missingFields.push('Thumbnail');
    }

    if (!event.category || event.category.trim() === '') {
      missingFields.push('Category');
    }

    if (!event.pricingPlanId || event.pricingPlanId.trim() === '') {
      missingFields.push('Price Plan');
    }

    if (!event.startTime || event.startTime.trim() === '') {
      missingFields.push('Start Time');
    }

    if (!event.endTime || event.endTime.trim() === '') {
      missingFields.push('End Time');
    }

    if (!event.timezone || event.timezone.trim() === '') {
      missingFields.push('Timezone');
    }

    if (missingFields.length > 0) {
      const fieldsList = missingFields.join(', ');
      toast.error(`Please fill in the following required fields before publishing: ${fieldsList}`);
      return;
    }

    setIsPublishing(true);

    try {
      const publishedEvent = await publishEvent(id);

      // Update the local state
      setEvent(publishedEvent);

      // Update the events list in the app context if needed
      if (events.length > 0) {
        setEvents(prev => prev.map(e => e.id === id ? publishedEvent : e));
      }

      toast.success("Event published successfully! It will now appear on the home page.");
    } catch (error) {
      console.error('Failed to publish event:', error);
      toast.error("Failed to publish event");
    } finally {
      setIsPublishing(false);
    }
  };

  const onSubmit = async (data: z.infer<typeof editEventSchema>) => {
    if (!event) return;

    setIsSubmitting(true);

    try {
      // Find the country name from the country code
      const selectedCountry = COUNTRIES.find(country => country.code === data.videoAddress.countryCode);
      const countryName = selectedCountry ? selectedCountry.name : "";

      // Convert datetime-local values to proper UTC time for the event's timezone
      // This ensures that when a user selects "2:00 PM", it's stored as 2:00 PM in the event's timezone
      const convertToUTC = (dateTimeString: string, timezone: string): string => {
        try {
          if (!dateTimeString) return "";

          // Parse the datetime-local input to get the date/time components
          const inputDate = new Date(dateTimeString);

          // Extract the components (these represent the user's intended local time)
          const year = inputDate.getFullYear();
          const month = inputDate.getMonth(); // 0-based
          const day = inputDate.getDate();
          const hours = inputDate.getHours();
          const minutes = inputDate.getMinutes();
          const seconds = inputDate.getSeconds();

          // Create an ISO string representing the local time
          const localTimeString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

          // Create a date assuming this time is UTC
          const assumedUtc = new Date(localTimeString + 'Z');

          // See what time this would be in the target timezone
          const inTargetTz = new Intl.DateTimeFormat('en-CA', {
            timeZone: timezone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
          }).format(assumedUtc);

          // Parse the result to see the difference
          const targetParts = inTargetTz.split(/[T\s-:]/);
          const targetYear = parseInt(targetParts[0]);
          const targetMonth = parseInt(targetParts[1]);
          const targetDay = parseInt(targetParts[2]);
          const targetHour = parseInt(targetParts[3]);
          const targetMinute = parseInt(targetParts[4]);
          const targetSecond = parseInt(targetParts[5]) || 0;

          // Calculate the difference
          const targetTime = new Date(targetYear, targetMonth - 1, targetDay, targetHour, targetMinute, targetSecond);
          const originalTime = new Date(year, month, day, hours, minutes, seconds); // month is already 0-based, don't subtract 1!
          const diff = targetTime.getTime() - originalTime.getTime();

          // Apply the reverse difference to get the correct UTC time
          const correctUtc = new Date(assumedUtc.getTime() - diff);

          return correctUtc.toISOString();
        } catch (error) {
          console.error('Error converting date to UTC:', error);
          // Fallback to simple conversion if timezone conversion fails
          return new Date(dateTimeString).toISOString();
        }
      };

      // Convert start and end times to UTC with timezone consideration
      const utcStartTime = convertToUTC(data.startTime, data.timezone);
      const utcEndTime = convertToUTC(data.endTime, data.timezone);

      // Update the event data
      const updatedEvent = await updateEvent(id, {
        title: data.title,
        description: data.description,
        startTime: utcStartTime,
        endTime: utcEndTime,
        timezone: data.timezone,
        duration: data.duration, // Send duration to backend
        forKids: data.forKids,
        viewersOverEighteen: data.viewersOverEighteen,
        videoAddress: {
          countryIso2: data.videoAddress.countryCode,
          streetName: data.videoAddress.streetName,
          streetNumber: data.videoAddress.streetNumber,
          province: data.videoAddress.province,
          postalCode: data.videoAddress.postalCode,
          countryName: countryName,
        },
        tags: data.tags,
        category: data.category,
        pricingPlanId: data.pricingPlanId,
      });

      // Update the local state
      setEvent(updatedEvent);

      // Update the events list in the app context if needed
      if (events.length > 0) {
        setEvents(prev => prev.map(e => e.id === id ? updatedEvent : e));
      }

      toast.success("Event updated successfully");
      router.push(`/event/${id}`);
    } catch (error) {
      console.error('Failed to update event:', error);
      toast.error("Failed to update event");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state if any data is still loading
  if (loading || loadingCategories || loadingPricingPlans) {
    return (
      <div className="container py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => router.push(`/event/${id}`)}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Event
        </Button>

        <div className="flex flex-col items-center justify-center h-60">
          <Loader2 className="h-10 w-10 animate-spin mb-4" />
          <p className="text-muted-foreground">Loading event data...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container py-8">
        <Button
          variant="ghost"
          className="mb-6 flex items-center gap-2"
          onClick={() => router.push(`/event/${id}`)}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Event
        </Button>

        <div className="flex flex-col items-center justify-center h-60">
          <p className="text-destructive font-medium mb-2">Error loading event data</p>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-8">
      <Button
        variant="ghost"
        className="mb-6 flex items-center gap-2"
        onClick={() => router.push(`/event/${id}`)}
      >
        <ArrowLeft className="h-4 w-4" />
        Back to Event
      </Button>

      <Heading
        title="Edit Event"
        description="Update your event details"
        className="mb-6"
      />

      <div className="max-w-2xl mx-auto">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="thumbnail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Thumbnail</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      {thumbnailPreview && (
                        <div className="rounded-lg overflow-hidden border border-gray-200">
                          <img
                            src={thumbnailPreview}
                            alt="Event thumbnail"
                            className="w-full aspect-video object-cover"
                          />
                        </div>
                      )}
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <Input
                            type="file"
                            accept="image/*"
                            onChange={handleThumbnailChange}
                            className="cursor-pointer"
                            disabled={isUploadingThumbnail}
                          />
                          {isUploadingThumbnail && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>Uploading...</span>
                            </div>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Recommended size: 1280x720 pixels (16:9 aspect ratio)
                        </p>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Title</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea className="min-h-[100px]" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Time</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        onChange={(e) => handleStartTimeChange(e.target.value)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Time</FormLabel>
                    <FormControl>
                      <Input
                        type="datetime-local"
                        {...field}
                        onChange={(e) => handleEndTimeChange(e.target.value)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (hours)</FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={MIN_EVENT_DURATION}
                          max={MAX_EVENT_DURATION}
                          step="0.5"
                          {...field}
                          onChange={(e) => handleDurationChange(parseFloat(e.target.value))}
                        />
                        <div className="text-xs text-muted-foreground whitespace-nowrap">
                          ({MIN_EVENT_DURATION}-{MAX_EVENT_DURATION} hours)
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="timezone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Timezone</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TIMEZONES.map((timezone) => (
                          <SelectItem key={timezone} value={timezone}>
                            {timezone}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="forKids"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>For Kids</FormLabel>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="viewersOverEighteen"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>18+ Content</FormLabel>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Video Address</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="videoAddress.streetNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Street Number</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="videoAddress.streetName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Street Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="videoAddress.province"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Province/State</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="videoAddress.postalCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Postal Code</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="videoAddress.countryCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select country" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {COUNTRIES.map((country) => (
                            <SelectItem key={country.code} value={country.code}>
                              {country.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={loadingCategories ? "Loading categories..." : "Select a category"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingCategories ? (
                          <SelectItem value="loading" disabled>Loading categories...</SelectItem>
                        ) : categories.length > 0 ? (
                          categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled>No categories available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pricingPlanId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pricing Plan</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={loadingPricingPlans ? "Loading pricing plans..." : "Select a pricing plan"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loadingPricingPlans ? (
                          <SelectItem value="loading" disabled>Loading pricing plans...</SelectItem>
                        ) : pricingPlans.length > 0 ? (
                          pricingPlans.map((plan) => (
                            <SelectItem key={plan.id} value={plan.id}>
                              {plan.product} - {plan.currency} {plan.totalPrice.toFixed(2)} ({plan.countryIso2})
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="none" disabled>No pricing plans available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-between items-center">
              {/* Status indicator */}
              {event && (
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">Status:</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    event.status === 'published'
                      ? 'bg-green-100 text-green-800'
                      : event.status === 'live'
                      ? 'bg-red-100 text-red-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {event.status === 'published'
                      ? 'Published'
                      : event.status === 'live'
                      ? 'Live'
                      : event.status === 'ended'
                      ? 'Ended'
                      : 'Draft'}
                  </span>
                </div>
              )}

              <div className="flex gap-4">
                {/* Publish button - only show if event is in draft status */}
                {event && event.status === 'draft' && (
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={handlePublishEvent}
                    disabled={isPublishing}
                    className="gap-2"
                  >
                    {isPublishing ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Publishing...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4" />
                        Publish Event
                      </>
                    )}
                  </Button>
                )}

                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push(`/event/${id}`)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HLS Player Test</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        video {
            width: 100%;
            max-width: 800px;
            height: auto;
            background: #000;
        }
        .info {
            margin: 20px 0;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🎬 HLS Player Test</h1>
    
    <div class="info">
        <h3>Testing AWS IVS HLS Stream with Authorization</h3>
        <p>This page tests if the HLS stream with authorization token works correctly.</p>
    </div>

    <video id="video" controls muted>
        <p>Your browser does not support the video tag.</p>
    </video>

    <div style="margin: 20px 0;">
        <button onclick="loadStream()">Load Authorized Stream</button>
        <button onclick="clearStream()">Clear Stream</button>
        <button onclick="toggleMute()">Toggle Mute</button>
    </div>

    <div id="status" class="info">
        <strong>Status:</strong> Ready to test
    </div>

    <div id="streamInfo" class="info" style="display: none;">
        <h4>Stream Information:</h4>
        <div id="streamDetails"></div>
    </div>

    <script>
        const video = document.getElementById('video');
        const statusDiv = document.getElementById('status');
        const streamInfoDiv = document.getElementById('streamInfo');
        const streamDetailsDiv = document.getElementById('streamDetails');
        
        let hls = null;

        // Your authorized stream URL (replace with the actual URL from your backend)
        const authorizedStreamUrl = 'https://71ce307069f0.eu-west-1.playback.live-video.net/api/video/v1/eu-west-1.223358806502.channel.y6EL0pKPjFNj.m3u8?token=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';

        function updateStatus(message, isError = false) {
            statusDiv.className = isError ? 'info error' : 'info success';
            statusDiv.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        function updateStreamInfo(info) {
            streamDetailsDiv.innerHTML = info;
            streamInfoDiv.style.display = 'block';
        }

        function loadStream() {
            updateStatus('Loading HLS stream...');
            
            // Cleanup existing HLS instance
            if (hls) {
                hls.destroy();
                hls = null;
            }

            if (Hls.isSupported()) {
                hls = new Hls({
                    enableWorker: true,
                    lowLatencyMode: true,
                    backBufferLength: 90,
                });

                hls.loadSource(authorizedStreamUrl);
                hls.attachMedia(video);

                hls.on(Hls.Events.MANIFEST_PARSED, function (event, data) {
                    updateStatus('✅ HLS manifest parsed successfully! Stream is ready to play.');
                    
                    const info = `
                        <p><strong>Levels:</strong> ${data.levels.length}</p>
                        <p><strong>First Level:</strong> ${data.levels[0]?.width}x${data.levels[0]?.height} @ ${data.levels[0]?.bitrate} bps</p>
                        <p><strong>Audio Tracks:</strong> ${data.audioTracks?.length || 0}</p>
                        <p><strong>URL:</strong> ${authorizedStreamUrl.substring(0, 100)}...</p>
                    `;
                    updateStreamInfo(info);
                    
                    console.log('HLS manifest parsed:', data);
                });

                hls.on(Hls.Events.ERROR, function (event, data) {
                    console.error('HLS error:', data);
                    
                    if (data.fatal) {
                        switch (data.type) {
                            case Hls.ErrorTypes.NETWORK_ERROR:
                                updateStatus('❌ Network error: ' + (data.details || 'Unknown network error'), true);
                                break;
                            case Hls.ErrorTypes.MEDIA_ERROR:
                                updateStatus('❌ Media error: ' + (data.details || 'Unknown media error'), true);
                                break;
                            default:
                                updateStatus('❌ Fatal error: ' + (data.details || 'Unknown error'), true);
                                break;
                        }
                    } else {
                        updateStatus('⚠️ Non-fatal error: ' + (data.details || 'Unknown error'), true);
                    }
                });

                hls.on(Hls.Events.LEVEL_LOADED, function (event, data) {
                    console.log('Level loaded:', data);
                    updateStatus('✅ Stream level loaded successfully');
                });

            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                // Native HLS support (Safari)
                video.src = authorizedStreamUrl;
                updateStatus('✅ Using native HLS support (Safari)');
                
                video.addEventListener('loadedmetadata', function() {
                    updateStatus('✅ Video metadata loaded successfully');
                });
                
                video.addEventListener('error', function(e) {
                    updateStatus('❌ Video error: ' + e.message, true);
                });
                
            } else {
                updateStatus('❌ HLS is not supported in this browser', true);
            }
        }

        function clearStream() {
            if (hls) {
                hls.destroy();
                hls = null;
            }
            video.src = '';
            updateStatus('Stream cleared');
            streamInfoDiv.style.display = 'none';
        }

        function toggleMute() {
            video.muted = !video.muted;
            updateStatus(`Video ${video.muted ? 'muted' : 'unmuted'}`);
        }

        // Video event listeners
        video.addEventListener('play', function() {
            updateStatus('▶️ Video started playing');
        });

        video.addEventListener('pause', function() {
            updateStatus('⏸️ Video paused');
        });

        video.addEventListener('waiting', function() {
            updateStatus('⏳ Video buffering...');
        });

        video.addEventListener('canplay', function() {
            updateStatus('✅ Video can start playing');
        });
    </script>
</body>
</html>

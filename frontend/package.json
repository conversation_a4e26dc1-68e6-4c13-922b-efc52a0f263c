{"name": "event-interactive-streams", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev", "dev:https": "next dev --experimental-https", "dev:https-custom": "node scripts/https-server.js", "dev:backend": "node scripts/start-backend.js", "dev:fullstack": "concurrently \"npm run dev:backend\" \"npm run dev:https-custom\"", "build": "next build", "start": "next start", "start:https": "next start --experimental-https", "lint": "next lint", "generate-cert": "node scripts/generate-cert.js", "setup-https": "npm run generate-cert && npm run dev:https-custom", "setup-fullstack": "npm run generate-cert && npm run dev:fullstack"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@stripe/react-stripe-js": "^2.9.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.56.2", "@types/hls.js": "^0.13.3", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "hls.js": "^1.6.3", "i18next": "^25.0.1", "input-otp": "^1.2.4", "jwt-decode": "^4.0.0", "keycloak-js": "^26.2.0", "lovable-tagger": "^1.1.8", "lucide-react": "^0.462.0", "next": "^15.0.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-i18next": "^14.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^7.6.1", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.15.2", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "eslint": "^9.9.0", "eslint-config-next": "^15.0.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "globals": "^15.9.0", "postcss": "^8.4.47", "readline-sync": "^1.4.10", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1"}}